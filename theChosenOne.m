%% VIPA 光谱仪 -CMQ1
%% 硬编码
% for m = 3300:1:3600 - m值范围应动态计算
% xi = 141:420; - Y坐标范围应用FSR参数
% expected_range = [1400, 1470]; - 波长范围应从CO2数据库获取
% coarse_angles = -2.3:0.01:-1.7; - 角度搜索范围应动态计算
% definput = {'320', '256'}; - 默认值应用图像尺寸一半
% m_range = 3300:1:3600; - 同第1条问题
% line_x = get_spline(img_x-800, ...) - 800这个偏移值硬编码
% line_g = get_spline(img_g-800, ...) - 同上
% line1 = -log((line_x(:,1)).*0.982./(line_g(:,1))); - 0.982校正因子硬编码
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% fine_range = 0.02; fine_step = 0.000001; - 精细搜索参数
% 'MinPeakProminence', 4, 'MinPeakDistance', 7 - 峰值检测参数
% pos = ceil(linspace(20, high-low-19, 5)); - 条纹采样参数
% min(3, sum(valid_idx)-1) - 多项式拟合最大阶数3硬编码
% for offset = -2:2 - 边界处理的±2范围硬编码
% x_stripe = round(loc(ceil(size(loc,1)/2), :)); - 取中间位置的1/2硬编码
%% 清除
clear all; clc; close all;
if exist('matlab.mat', 'file')
    delete('matlab.mat');
    fprintf('已删除工作空间缓存文件\n');
end
asv_files = dir('*.asv');
if ~isempty(asv_files)
    for i = 1:length(asv_files)
        delete(asv_files(i).name);
    end
    fprintf('已删除 %d 个自动保存文件\n', length(asv_files));
end
close all force; % 强制关闭所有MATLAB图形窗口;强制关闭所有残留的对话框
%% 1 读取图片
img_g = double(imread('2.tiff'));
img_x = double(imread('1.tiff'));
img_d = double(imread('0.tiff'));
j_g = createInteractiveImage(img_g, 'Reference Image', '参考图像');
j_x = createInteractiveImage(img_x, 'Calibration Image', '标定图像');
j_d = createInteractiveImage(img_d, 'Dark Image', '黑暗背景');
img = -log((img_x)./(img_g));
j_a = createInteractiveImage(img, 'Absorption Spectrum', '吸收光谱');
%% 2 交互，找FSR
j_b = createInteractiveImage(img, 'Absorption Spectrum', '吸收光谱');
fprintf('请观察吸收光谱图，按回车键继续设置FSR参数...\n');
pause; 
fsr_values = inputFSRDialog();
%% 3 条纹识别算法-根据条纹提取相对强度，loc为条纹中心坐标
loc = get_loc(img_g, fsr_values.high, fsr_values.low);
line_x = get_spline(img_x-800, fsr_values.high, fsr_values.low, loc);
line_g = get_spline(img_g-800, fsr_values.high, fsr_values.low, loc);
line1 = -log((line_x(:,1)).*0.982./(line_g(:,1)));
line2 = flip(line1);
figure('Name', 'VIPA Spectrum Result - VIPA光谱结果', 'NumberTitle', 'off');
plot(line1, '.-', 'LineWidth', 1.5, 'MarkerSize', 8);
title('一维谱图-波长-找特征点波长用', 'FontSize', 12, 'FontName', 'SimHei');
xlabel('像素位置', 'FontSize', 10, 'FontName', 'SimHei');
ylabel('相对强度', 'FontSize', 10, 'FontName', 'SimHei');
grid on;
figure('Name', 'VIPA Spectrum Result - VIPA光谱结果', 'NumberTitle', 'off');
plot(line2, '.-', 'LineWidth', 1.5, 'MarkerSize', 8);
title('一维谱图-波数-不能找波长', 'FontSize', 12, 'FontName', 'SimHei');
xlabel('像素位置', 'FontSize', 10, 'FontName', 'SimHei');
ylabel('相对强度', 'FontSize', 10, 'FontName', 'SimHei');
grid on;
%% 4 像素数~像素强度~像素坐标~所在干涉条纹编号
pixel_count = length(line1);
[~, fringe_count] = size(loc);  % 获取检测到的条纹数量
pixels_per_fringe = pixel_count / fringe_count;  % 每个条纹的像素数

pixel_data = zeros(pixel_count, 5);  % 预分配数组：[像素数, 像素强度, X坐标, Y坐标, 条纹编号]
for i = 1:pixel_count
    pixel_data(i, 1) = i;  % 像素数（从1开始）
    pixel_data(i, 2) = line1(i);  % 像素强度（波长图的Y轴数据）
    pixel_data(i, 3) = line_g(i, 3);  % X坐标（列位置）
    pixel_data(i, 4) = line_g(i, 2) + fsr_values.low - 1;  % Y坐标（行位置，需要加上偏移）
    pixel_data(i, 5) = ceil(i / pixels_per_fringe);  % 条纹编号（从1开始）
end
pixel_table = table(pixel_data(:,1), pixel_data(:,2), pixel_data(:,3), pixel_data(:,4), pixel_data(:,5), ...
    'VariableNames', {'像素数', '像素强度', '像素坐标X', '像素坐标Y', '条纹编号'});
output_filename = '像素数-强度-坐标-所在条纹编号.xlsx';
writetable(pixel_table, output_filename);
fprintf('像素坐标对应表已保存到文件: %s\n', output_filename);
fprintf('总共包含 %d 个像素点的坐标信息\n', pixel_count);
fprintf('检测到 %d 条干涉条纹\n', fringe_count);
fprintf('每条条纹包含 %.0f 个像素点\n', pixels_per_fringe);
fprintf('像素强度范围: %.3f - %.3f\n', min(pixel_data(:,2)), max(pixel_data(:,2)));
fprintf('X坐标范围: %d - %d\n', min(pixel_data(:,3)), max(pixel_data(:,3)));
fprintf('Y坐标范围: %d - %d\n', min(pixel_data(:,4)), max(pixel_data(:,4)));
fprintf('条纹编号范围: %d - %d\n', min(pixel_data(:,5)), max(pixel_data(:,5)));
fprintf('\n数据预览:\n');
fprintf('像素数\t像素强度\t\t像素坐标X\t像素坐标Y\t条纹编号\n');
% 显示前3行
for i = 1:min(3, pixel_count)
    fprintf('%d\t\t%.3f\t\t%d\t\t\t%d\t\t\t%d\n', pixel_data(i,1), pixel_data(i,2), pixel_data(i,3), pixel_data(i,4), pixel_data(i,5));
end
% 如果数据超过3行，显示省略号和最后一行
if pixel_count > 3
    fprintf('...\n');
    fprintf('%d\t\t%.3f\t\t%d\t\t\t%d\t\t\t%d\n', pixel_data(pixel_count,1), pixel_data(pixel_count,2), pixel_data(pixel_count,3), pixel_data(pixel_count,4), pixel_data(pixel_count,5));
end

% 保存关键变量到workspace供后续步骤使用
assignin('base', 'line1', line1);
assignin('base', 'line_g', line_g);
assignin('base', 'loc', loc);
assignin('base', 'fsr_values', fsr_values);
assignin('base', 'pixel_data', pixel_data);
fprintf('✅ 关键变量已保存到workspace\n');

%% 5 数据库-波长图-波数图
co2_data = readmatrix('CO2.par.xlsx');
wavelength = co2_data(:, 1);    % 第1列：波长
wavenumber = co2_data(:, 3);    % 第3列：波数
intensity = co2_data(:, 4);     % 第4列：相对强度
figure('Name', 'CO2 Database - 波长强度图', 'NumberTitle', 'off');
plot(wavelength, intensity, '.-', 'LineWidth', 1.5, 'MarkerSize', 8);
title('CO2数据库-波长强度图', 'FontSize', 12, 'FontName', 'SimHei');
xlabel('波长', 'FontSize', 10, 'FontName', 'SimHei');
ylabel('相对强度', 'FontSize', 10, 'FontName', 'SimHei');
grid on;
figure('Name', 'CO2 Database - 波数强度图', 'NumberTitle', 'off');
plot(wavenumber, intensity, '.-', 'LineWidth', 1.5, 'MarkerSize', 8);
title('CO2数据库-波数强度图', 'FontSize', 12, 'FontName', 'SimHei');
xlabel('波数', 'FontSize', 10, 'FontName', 'SimHei');
ylabel('相对强度', 'FontSize', 10, 'FontName', 'SimHei');
grid on;
%% 6 交互，用户找寻，波长特征点
calibration_window = createCalibrationWindow(line1, wavelength, intensity, img, pixel_table);
assignin('base', 'calibration_window', calibration_window);
%% 7 交互，相邻FSR的同一波长点对，旋转特征点
% 此步骤通过第6步的"下一步"按钮触发，不在此处直接执行
% 用户需要在第6步完成波长标定后，点击"下一步"按钮进入第7步
%% 8 找旋转角度-感谢昊哥赞助一个函数
% 此步骤通过第7步的"下一步"按钮触发，不在此处直接执行
% 用户需要在第7步完成旋转标定后，点击"下一步"按钮进入第8步
%% 9 坐标旋转校正
% 此步骤由第8步自动触发，不在此处直接执行
%% 10 迭代求解 m A B C
% 此步骤由第9步自动触发，不在此处直接执行
%% 11 表格波长~逐行波长
% 此步骤由第10步自动触发，不在此处直接执行
%% 12 波长映射到一维谱图
% 此步骤由第11步自动触发，不在此处直接执行
%% 13 M-像素坐标Y-波长
%% 14 二次多项式拟合结果图
%% 15 暂无



%% 内嵌函数：创建旋转标定窗口
function rotation_window = createRotationCalibrationWindow(img, titleEn, titleCn, x_coords, y_coords)
    % 创建主窗口
    rotation_window = figure('Name', '旋转角度标定系统', 'NumberTitle', 'off', ...
        'Position', [50, 50, 1400, 900], 'MenuBar', 'none', 'ToolBar', 'none');

    % 左侧面板：图像显示
    panel_left = uipanel('Parent', rotation_window, 'Position', [0.05, 0.1, 0.5, 0.85], ...
        'Title', '吸收光谱图', 'FontSize', 12, 'FontName', 'SimHei');

    % 右侧面板：表格和按钮
    panel_right = uipanel('Parent', rotation_window, 'Position', [0.6, 0.1, 0.35, 0.85], ...
        'Title', '旋转特征点标定', 'FontSize', 12, 'FontName', 'SimHei');

    % 在左侧面板创建图像显示
    createRotationImageDisplay(panel_left, img, titleEn, titleCn);

    % 在右侧面板创建表格和按钮
    createRotationTableAndButtons(panel_right, x_coords, y_coords);
end

%% 内嵌函数：创建旋转标定图像显示
function createRotationImageDisplay(parent_panel, img, titleEn, titleCn)
    % 创建坐标轴
    axes_img = axes('Parent', parent_panel, 'Position', [0.1, 0.15, 0.8, 0.75]);

    % 显示图像
    imagesc(axes_img, img);
    colormap(axes_img, jet);
    colorbar(axes_img);
    axis(axes_img, 'image');
    axis(axes_img, 'off');

    % 设置标题
    if nargin >= 4 && ~isempty(titleCn)
        title(axes_img, [titleEn ' - ' titleCn], 'FontSize', 12, 'FontName', 'SimHei');
    elseif nargin >= 3 && ~isempty(titleEn)
        title(axes_img, titleEn, 'FontSize', 12);
    end

    % 创建信息显示框
    info_text = uicontrol('Parent', parent_panel, 'Style', 'text', ...
        'Position', [10, 10, 200, 60], ...
        'String', '像素坐标: (-, -)\n相对强度: -', ...
        'FontSize', 10, ...
        'BackgroundColor', 'white', ...
        'HorizontalAlignment', 'left', ...
        'FontName', 'SimHei');

    % 设置鼠标移动回调函数
    parent_fig = ancestor(parent_panel, 'figure');
    set(parent_fig, 'WindowButtonMotionFcn', @(src, event) updateRotationImageInfo(src, event, axes_img, img, info_text));

    % 添加图像工具栏
    addRotationImageToolbar(parent_panel, axes_img);
end

%% 内嵌函数：创建旋转标定表格和按钮
function createRotationTableAndButtons(parent_panel, x_coords, y_coords)
    % 创建表格数据（50行4列）
    table_data = cell(50, 4);

    % 填入第1、2列数据（X1, Y1）
    data_count = min(length(x_coords), 50);
    for i = 1:data_count
        table_data{i, 1} = num2str(x_coords(i));  % X1
        table_data{i, 2} = num2str(y_coords(i));  % Y1
        table_data{i, 3} = '';  % X2 - 用户输入
        table_data{i, 4} = '';  % Y2 - 用户输入
    end

    % 其余行保持空白
    for i = (data_count + 1):50
        table_data{i, 1} = '';
        table_data{i, 2} = '';
        table_data{i, 3} = '';
        table_data{i, 4} = '';
    end

    % 创建表格
    column_names = {'X1', 'Y1', 'X2', 'Y2'};
    column_editable = [false, false, true, true];  % 只有第3、4列可编辑

    rotation_table = uitable('Parent', parent_panel, ...
        'Position', [10, 80, 420, 600], ...
        'Data', table_data, ...
        'ColumnName', column_names, ...
        'ColumnEditable', column_editable, ...
        'ColumnWidth', {100, 100, 100, 100}, ...
        'FontSize', 9, ...
        'FontName', 'SimHei');

    % 创建按钮
    createRotationButtons(parent_panel, rotation_table);

    fprintf('旋转标定表格已创建，导入了 %d 个坐标点\n', data_count);
end

%% 内嵌函数：创建旋转标定按钮
function createRotationButtons(parent_panel, rotation_table)
    % 按钮1：导入
    uicontrol('Parent', parent_panel, 'Style', 'pushbutton', ...
        'Position', [10, 40, 100, 30], ...
        'String', '导入', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) importRotationData(rotation_table));

    % 按钮2：导出
    uicontrol('Parent', parent_panel, 'Style', 'pushbutton', ...
        'Position', [120, 40, 100, 30], ...
        'String', '导出', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) exportRotationData(rotation_table));

    % 按钮3：下一步
    uicontrol('Parent', parent_panel, 'Style', 'pushbutton', ...
        'Position', [230, 40, 100, 30], ...
        'String', '下一步', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) nextStepRotation());
end

%% 内嵌函数：创建交互式图像显示
function h_fig = createInteractiveImage(img, titleEn, titleCn)
    % createInteractiveImage - 创建交互式图像显示窗口
    %
    % 输入参数:
    %   img - 图像数据矩阵
    %   titleEn - 英文标题
    %   titleCn - 中文标题
    %
    % 输出参数:
    %   h_fig - 图形窗口句柄

    % 创建新的图形窗口
    h_fig = figure;

    % 设置窗口标题
    if nargin >= 3 && ~isempty(titleCn)
        set(h_fig, 'Name', [titleEn ' - ' titleCn], 'NumberTitle', 'off');
    elseif nargin >= 2 && ~isempty(titleEn)
        set(h_fig, 'Name', titleEn, 'NumberTitle', 'off');
    else
        set(h_fig, 'Name', 'Image Display', 'NumberTitle', 'off');
    end

    % 显示图像
    imagesc(img);

    % 设置颜色映射为彩色
    colormap(jet);

    % 添加颜色条
    colorbar;

    % 设置坐标轴属性
    axis image;  % 保持图像纵横比
    axis off;    % 隐藏坐标轴

    % 添加标题
    if nargin >= 3 && ~isempty(titleCn)
        title([titleEn ' - ' titleCn], 'FontSize', 12);
    elseif nargin >= 2 && ~isempty(titleEn)
        title(titleEn, 'FontSize', 12);
    end

    % 创建信息显示框
    info_text = uicontrol('Style', 'text', ...
        'Position', [10, 10, 200, 60], ...
        'String', '像素坐标: (-, -)\n相对强度: -', ...
        'FontSize', 10, ...
        'BackgroundColor', 'white', ...
        'HorizontalAlignment', 'left', ...
        'FontName', 'SimHei');  % 使用支持中文的字体

    % 设置鼠标移动回调函数
    set(h_fig, 'WindowButtonMotionFcn', @(src, event) updateInfo(src, event, img, info_text));

    % 设置窗口位置（可选）
    set(h_fig, 'Position', [100, 100, 700, 600]);

    % 内嵌的鼠标移动回调函数
    function updateInfo(src, ~, img, info_text)
        % 获取当前鼠标位置
        current_point = get(gca, 'CurrentPoint');

        % 获取图像尺寸
        [img_height, img_width] = size(img);

        % 获取坐标轴范围
        xlim_range = get(gca, 'XLim');
        ylim_range = get(gca, 'YLim');

        % 获取鼠标在图像中的位置
        x_pos = current_point(1, 1);
        y_pos = current_point(1, 2);

        % 检查鼠标是否在图像范围内
        if x_pos >= xlim_range(1) && x_pos <= xlim_range(2) && ...
           y_pos >= ylim_range(1) && y_pos <= ylim_range(2)

            % 将坐标转换为像素坐标（四舍五入到最近的整数）
            pixel_x = round(x_pos);
            pixel_y = round(y_pos);

            % 确保坐标在有效范围内
            if pixel_x >= 1 && pixel_x <= img_width && ...
               pixel_y >= 1 && pixel_y <= img_height

                % 获取像素值
                intensity = img(pixel_y, pixel_x);

                % 更新信息显示
                info_str = sprintf('像素坐标: (%d, %d)\n相对强度: %.3f', ...
                    pixel_x, pixel_y, intensity);
                set(info_text, 'String', info_str);
            else
                % 鼠标在图像边界外
                set(info_text, 'String', '像素坐标: (-, -)\n相对强度: -');
            end
        else
            % 鼠标不在图像区域内
            set(info_text, 'String', '像素坐标: (-, -)\n相对强度: -');
        end
    end
end

%% 内嵌函数：FSR设置对话框
function fsr_values = inputFSRDialog()
    % 创建对话框
    d = dialog('Position', [300, 300, 400, 200], 'Name', 'VIPA光谱仪FSR设置');

    % 创建标题文本
    uicontrol('Parent', d, 'Style', 'text', 'Position', [50, 150, 300, 30], ...
        'String', '请设置VIPA光谱仪的FSR参数', 'FontSize', 12, 'FontName', 'SimHei');

    % 创建Low FSR标签和输入框
    uicontrol('Parent', d, 'Style', 'text', 'Position', [50, 110, 80, 25], ...
        'String', 'FSR Low:', 'FontSize', 10, 'FontName', 'SimHei');
    low_edit = uicontrol('Parent', d, 'Style', 'edit', 'Position', [140, 110, 100, 25], ...
        'String', '0', 'FontSize', 10);

    % 创建High FSR标签和输入框
    uicontrol('Parent', d, 'Style', 'text', 'Position', [50, 70, 80, 25], ...
        'String', 'FSR High:', 'FontSize', 10, 'FontName', 'SimHei');
    high_edit = uicontrol('Parent', d, 'Style', 'edit', 'Position', [140, 70, 100, 25], ...
        'String', '100', 'FontSize', 10);

    % 创建确定按钮
    uicontrol('Parent', d, 'Style', 'pushbutton', 'Position', [100, 20, 80, 30], ...
        'String', '确定', 'FontSize', 10, 'FontName', 'SimHei', ...
        'Callback', @(~,~) uiresume(d));

    % 创建取消按钮
    uicontrol('Parent', d, 'Style', 'pushbutton', 'Position', [220, 20, 80, 30], ...
        'String', '取消', 'FontSize', 10, 'FontName', 'SimHei', ...
        'Callback', @(~,~) delete(d));

    % 等待用户操作
    uiwait(d);

    % 检查对话框是否仍然存在（用户点击了确定）
    if ishandle(d)
        % 获取输入值
        fsr_low = str2double(get(low_edit, 'String'));
        fsr_high = str2double(get(high_edit, 'String'));

        % 验证输入
        if isnan(fsr_low) || isnan(fsr_high)
            errordlg('请输入有效的数值！', '输入错误', 'modal');
            delete(d);
            fsr_values = inputFSRDialog();  % 递归调用重新输入
        elseif fsr_low >= fsr_high
            errordlg('FSR Low 必须小于 FSR High！', '输入错误', 'modal');
            delete(d);
            fsr_values = inputFSRDialog();  % 递归调用重新输入
        else
            % 输入有效，返回结果
            fsr_values.low = fsr_low;
            fsr_values.high = fsr_high;

            % 显示确认信息
            fprintf('FSR设置完成：Low = %.2f, High = %.2f\n', fsr_low, fsr_high);

            delete(d);
        end
    else
        % 用户取消了操作
        fprintf('用户取消了FSR设置\n');
        fsr_values.low = NaN;
        fsr_values.high = NaN;
    end
end

%% 内嵌函数：获取条纹中心坐标
function loc = get_loc(img, high, low)
    pos = ceil(linspace(20, high-low-19, 5));
    [a, b] = size(img);
    img = img(low:high, :);

    % 初始化lsor数组
    lsor = [];

    for i = 1:5
        x_line = img(pos(i), :);
        [~, peaks_loc] = findpeaks(x_line, 'MinPeakProminence', 4, 'MinPeakDistance', 7);

        % 确保所有行有相同的峰值数量，填充NaN如果需要
        if i == 1
            max_peaks = length(peaks_loc);
            lsor = NaN(max_peaks, 5);
        end

        if length(peaks_loc) <= size(lsor, 1)
            lsor(1:length(peaks_loc), i) = peaks_loc;
        end
    end

    x = 1:high-low+1;
    loc = [];

    for i = 1:size(lsor, 1)
        % 只处理有效的峰值数据（非NaN）
        valid_idx = ~isnan(lsor(i, :));
        if sum(valid_idx) >= 3  % 至少需要3个点来拟合3次多项式
            p = polyfit(pos(valid_idx), lsor(i, valid_idx), min(3, sum(valid_idx)-1));
            loc(i, :) = polyval(p, x);
        end
    end

    if ~isempty(loc)
        loc = loc';
    else
        loc = [];
    end
end

%% 内嵌函数：样条插值提取条纹数据
function line = get_spline(img, high, low, loc)
    img = img(low:high, :);
    [a, b] = size(loc);
    data_out = zeros(a*b, 3);  % 预分配3列
    k = 1;

    for j = 1:1:b
        for i = a:-1:1
            % 检查边界条件，避免越界
            center_pos = round(loc(i, j));
            if center_pos > 2 && center_pos < size(img, 2) - 1
                % 计算5个相邻像素的和
                data_out(k, 1) = img(i, center_pos-2) + img(i, center_pos-1) + ...
                                img(i, center_pos) + img(i, center_pos+1) + img(i, center_pos+2);
                data_out(k, 2) = i;
                data_out(k, 3) = center_pos;
            else
                % 边界情况，只使用可用的像素
                sum_val = 0;
                count = 0;
                for offset = -2:2
                    pos = center_pos + offset;
                    if pos >= 1 && pos <= size(img, 2)
                        sum_val = sum_val + img(i, pos);
                        count = count + 1;
                    end
                end
                data_out(k, 1) = sum_val;
                data_out(k, 2) = i;
                data_out(k, 3) = center_pos;
            end
            k = k + 1;
        end
    end

    line = data_out;
end

%% 内嵌函数：创建交互式波长标定窗口
function calibration_window = createCalibrationWindow(line1, wavelength, intensity, img, pixel_table)
    % 创建主窗口
    calibration_window = figure('Name', '交互式波长标定系统', 'NumberTitle', 'off', ...
        'Position', [100, 100, 1400, 900], 'MenuBar', 'none', 'ToolBar', 'none');

    % 创建2x2布局的面板
    % 左上角：实测强度一维谱图
    panel_lu = uipanel('Parent', calibration_window, 'Position', [0.05, 0.55, 0.4, 0.4], ...
        'Title', '实测强度一维谱图', 'FontSize', 12, 'FontName', 'SimHei');

    % 左下角：CO2数据库波长谱图
    panel_ld = uipanel('Parent', calibration_window, 'Position', [0.05, 0.1, 0.4, 0.4], ...
        'Title', 'CO2数据库波长谱图', 'FontSize', 12, 'FontName', 'SimHei');

    % 右上角：吸收亮点图
    panel_ru = uipanel('Parent', calibration_window, 'Position', [0.5, 0.55, 0.45, 0.4], ...
        'Title', '吸收光谱图', 'FontSize', 12, 'FontName', 'SimHei');

    % 右下角：标定数据表格和按钮
    panel_rd = uipanel('Parent', calibration_window, 'Position', [0.5, 0.1, 0.45, 0.4], ...
        'Title', '标定数据表格', 'FontSize', 12, 'FontName', 'SimHei');

    % 在各个面板中创建图形
    createSubPlots(panel_lu, panel_ld, panel_ru, panel_rd, line1, wavelength, intensity, img, pixel_table);
end

%% 内嵌函数：创建子图和控件
function createSubPlots(panel_lu, panel_ld, panel_ru, panel_rd, line1, wavelength, intensity, img, pixel_table)
    % 左上角：实测强度一维谱图
    axes_lu = axes('Parent', panel_lu, 'Position', [0.1, 0.25, 0.85, 0.65]);
    plot(axes_lu, line1, '.-', 'LineWidth', 1.5, 'MarkerSize', 8);
    title(axes_lu, '一维谱图-波长-找特征点波长用', 'FontSize', 10, 'FontName', 'SimHei');
    xlabel(axes_lu, '像素位置', 'FontSize', 9, 'FontName', 'SimHei');
    ylabel(axes_lu, '相对强度', 'FontSize', 9, 'FontName', 'SimHei');
    grid(axes_lu, 'on');

    % 设置数据光标格式（像素数显示整数）
    setupPixelDataCursor(axes_lu);

    % 添加工具栏
    addPlotToolbar(panel_lu, axes_lu);

    % 为左上角图添加坐标显示
    info_text_lu = uicontrol('Parent', panel_lu, 'Style', 'text', ...
        'Position', [10, 10, 150, 40], ...
        'String', '像素位置: -\n相对强度: -', ...
        'FontSize', 8, ...
        'BackgroundColor', 'white', ...
        'HorizontalAlignment', 'left', ...
        'FontName', 'SimHei');

    % 左下角：CO2数据库波长谱图
    axes_ld = axes('Parent', panel_ld, 'Position', [0.1, 0.25, 0.85, 0.65]);
    plot(axes_ld, wavelength, intensity, '.-', 'LineWidth', 1.5, 'MarkerSize', 8);
    title(axes_ld, 'CO2数据库-波长强度图', 'FontSize', 10, 'FontName', 'SimHei');
    xlabel(axes_ld, '波长', 'FontSize', 9, 'FontName', 'SimHei');
    ylabel(axes_ld, '相对强度', 'FontSize', 9, 'FontName', 'SimHei');
    grid(axes_ld, 'on');

    % 设置数据光标格式（波长显示6位小数）
    setupPixelDataCursor(axes_ld);

    % 添加工具栏
    addPlotToolbar(panel_ld, axes_ld);

    % 为左下角图添加坐标显示
    info_text_ld = uicontrol('Parent', panel_ld, 'Style', 'text', ...
        'Position', [10, 10, 150, 40], ...
        'String', '波长: -\n相对强度: -', ...
        'FontSize', 8, ...
        'BackgroundColor', 'white', ...
        'HorizontalAlignment', 'left', ...
        'FontName', 'SimHei');

    % 右上角：吸收光谱图（保持原有交互功能）
    axes_ru = axes('Parent', panel_ru, 'Position', [0.1, 0.25, 0.8, 0.65]);
    info_text_ru = createAbsorptionImage(axes_ru, img);

    % 添加工具栏
    addImageToolbar(panel_ru, axes_ru);

    % 设置鼠标移动回调函数（统一处理三个图）
    parent_fig = ancestor(panel_lu, 'figure');
    set(parent_fig, 'WindowButtonMotionFcn', @(src, event) updateAllCoordinates(src, event, ...
        axes_lu, axes_ld, axes_ru, line1, wavelength, intensity, img, ...
        info_text_lu, info_text_ld, info_text_ru));

    % 右下角：创建表格和按钮
    createTableAndButtons(panel_rd, pixel_table);
end

%% 内嵌函数：创建吸收光谱图（保持原有交互功能）
function info_text_ru = createAbsorptionImage(axes_ru, img)
    % 在指定坐标轴中显示图像
    imagesc(axes_ru, img);
    colormap(axes_ru, jet);
    colorbar(axes_ru);
    axis(axes_ru, 'image');
    axis(axes_ru, 'off');
    title(axes_ru, '吸收光谱图', 'FontSize', 10, 'FontName', 'SimHei');

    % 创建信息显示框（在面板内）
    parent_panel = get(axes_ru, 'Parent');
    info_text_ru = uicontrol('Parent', parent_panel, 'Style', 'text', ...
        'Position', [10, 10, 150, 40], ...
        'String', '像素坐标: (-, -)\n相对强度: -', ...
        'FontSize', 8, ...
        'BackgroundColor', 'white', ...
        'HorizontalAlignment', 'left', ...
        'FontName', 'SimHei');
end

%% 内嵌函数：创建表格和按钮
function createTableAndButtons(panel_rd, pixel_table)
    % 创建表格数据（50行5列）
    table_data = cell(50, 5);
    for i = 1:50
        table_data{i, 1} = '';  % 像素数
        table_data{i, 2} = '';  % 像素坐标X
        table_data{i, 3} = '';  % 像素坐标Y
        table_data{i, 4} = '';  % 波长值
        table_data{i, 5} = '';  % 所在条纹编号
    end

    % 创建表格
    column_names = {'像素数', '像素坐标X', '像素坐标Y', '波长值', '所在条纹编号'};
    column_editable = [true, false, false, true, false];  % 只有第1、4列可编辑

    calibration_table = uitable('Parent', panel_rd, ...
        'Position', [10, 60, 420, 280], ...
        'Data', table_data, ...
        'ColumnName', column_names, ...
        'ColumnEditable', column_editable, ...
        'ColumnWidth', {70, 80, 80, 80, 90}, ...
        'FontSize', 9, ...
        'FontName', 'SimHei');

    % 创建按钮
    createCalibrationButtons(panel_rd, calibration_table, pixel_table);
end

%% 内嵌函数：创建标定按钮
function createCalibrationButtons(panel_rd, calibration_table, pixel_table)
    % 按钮1：自动计算235列
    uicontrol('Parent', panel_rd, 'Style', 'pushbutton', ...
        'Position', [10, 20, 100, 30], ...
        'String', '自动计算235列', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) autoCalculateColumns(calibration_table, pixel_table));

    % 按钮2：导出
    uicontrol('Parent', panel_rd, 'Style', 'pushbutton', ...
        'Position', [120, 20, 80, 30], ...
        'String', '导出', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) exportCalibrationData(calibration_table));

    % 按钮3：导入
    uicontrol('Parent', panel_rd, 'Style', 'pushbutton', ...
        'Position', [210, 20, 80, 30], ...
        'String', '导入', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) importCalibrationData(calibration_table));

    % 按钮4：下一步
    uicontrol('Parent', panel_rd, 'Style', 'pushbutton', ...
        'Position', [300, 20, 80, 30], ...
        'String', '下一步', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) nextStep());

    % 按钮5：清空
    uicontrol('Parent', panel_rd, 'Style', 'pushbutton', ...
        'Position', [390, 20, 80, 30], ...
        'String', '清空', ...
        'FontSize', 9, 'FontName', 'SimHei', ...
        'Callback', @(~,~) clearCalibrationTable(calibration_table));
end

%% 内嵌函数：自动计算235列
function autoCalculateColumns(calibration_table, pixel_table)
    try
        % 获取表格数据
        table_data = get(calibration_table, 'Data');

        % 读取像素数据Excel文件
        pixel_data = readmatrix('像素数-强度-坐标-所在条纹编号.xlsx');

        % 遍历表格中的每一行
        for i = 1:size(table_data, 1)
            pixel_num_str = table_data{i, 1};
            if ~isempty(pixel_num_str) && ~isnan(str2double(pixel_num_str))
                pixel_num = str2double(pixel_num_str);

                % 在像素数据中查找对应行（第1列是像素数）
                idx = find(pixel_data(:, 1) == pixel_num);
                if ~isempty(idx)
                    % 填充第2、3、5列
                    table_data{i, 2} = num2str(pixel_data(idx(1), 3));  % 像素坐标X
                    table_data{i, 3} = num2str(pixel_data(idx(1), 4));  % 像素坐标Y
                    table_data{i, 5} = num2str(pixel_data(idx(1), 5));  % 条纹编号
                end
            end
        end

        % 更新表格
        set(calibration_table, 'Data', table_data);
        msgbox('自动计算完成！', '成功', 'help');

    catch ME
        msgbox(['计算失败：' ME.message], '错误', 'error');
    end
end

%% 内嵌函数：导出标定数据
function exportCalibrationData(calibration_table)
    try
        % 获取表格数据
        table_data = get(calibration_table, 'Data');

        % 转换为数值矩阵（处理空值）
        export_data = [];
        for i = 1:size(table_data, 1)
            row_data = [];
            for j = 1:size(table_data, 2)
                if isempty(table_data{i, j}) || strcmp(table_data{i, j}, '')
                    row_data(j) = NaN;
                else
                    row_data(j) = str2double(table_data{i, j});
                end
            end
            export_data = [export_data; row_data];
        end

        % 生成文件名（自动递增序号）
        base_name = '像素数-坐标-波长-条纹编号';
        counter = 1;
        while exist([base_name '_' sprintf('%03d', counter) '.xlsx'], 'file')
            counter = counter + 1;
        end
        filename = [base_name '_' sprintf('%03d', counter) '.xlsx'];

        % 添加表头
        header = {'像素数', '像素坐标X', '像素坐标Y', '波长值', '所在条纹编号'};

        % 导出到Excel
        writematrix(export_data, filename, 'Sheet', 1, 'Range', 'A2');
        writecell(header, filename, 'Sheet', 1, 'Range', 'A1');
        msgbox(['数据已导出到：' filename], '导出成功', 'help');

    catch ME
        msgbox(['导出失败：' ME.message], '错误', 'error');
    end
end

%% 内嵌函数：清空标定表格
function clearCalibrationTable(calibration_table)
    try
        % 确认对话框
        choice = questdlg('确定要清空所有标定数据吗？', '确认清空', '是', '否', '否');
        if strcmp(choice, '是')
            % 清空表格数据
            empty_data = cell(20, 4);  % 创建20行4列的空数据
            set(calibration_table, 'Data', empty_data);
            msgbox('标定数据已清空', '操作完成', 'help');
        end
    catch ME
        msgbox(['清空失败: ' ME.message], '错误', 'error');
    end
end

%% 内嵌函数：导入标定数据
function importCalibrationData(calibration_table)
    try
        % 选择文件
        [filename, pathname] = uigetfile('*.xlsx', '选择要导入的标定数据文件');
        if filename == 0
            return;  % 用户取消
        end

        % 读取Excel文件
        import_data = readmatrix(fullfile(pathname, filename));

        % 转换为cell格式
        table_data = cell(50, 5);
        for i = 1:min(size(import_data, 1), 50)
            if i <= size(import_data, 1)
                table_data{i, 1} = num2str(import_data(i, 1));  % 像素数
                table_data{i, 2} = num2str(import_data(i, 2));  % 像素坐标X
                table_data{i, 3} = num2str(import_data(i, 3));  % 像素坐标Y
                table_data{i, 4} = num2str(import_data(i, 4));  % 波长值
                table_data{i, 5} = num2str(import_data(i, 5));  % 所在条纹编号
            end
        end

        % 更新表格
        set(calibration_table, 'Data', table_data);
        msgbox(['数据已从 ' filename ' 导入成功！'], '导入成功', 'help');

    catch ME
        msgbox(['导入失败：' ME.message], '错误', 'error');
    end
end

%% 内嵌函数：下一步操作
function nextStep()
    try
        % 弹出数据源选择对话框
        choice = questdlg('用于旋转角度标定的参考点对选择哪种方式导入？', ...
                          '数据导入选择', ...
                          '上一步表格中', '上一步导出的EXCEL', '上一步表格中');

        if isempty(choice) || strcmp(choice, '')
            return;  % 用户取消
        end

        x_coords = [];
        y_coords = [];

        if strcmp(choice, '上一步表格中')
            % 从当前表格中读取数据
            table_handle = findobj(gcf, 'Type', 'uitable');
            if ~isempty(table_handle)
                table_data = get(table_handle, 'Data');

                % 提取第2、3列数据（像素坐标X、Y）
                for i = 1:size(table_data, 1)
                    x_str = table_data{i, 2};
                    y_str = table_data{i, 3};

                    if ~isempty(x_str) && ~isempty(y_str) && ...
                       ~strcmp(x_str, '') && ~strcmp(y_str, '')
                        x_val = str2double(x_str);
                        y_val = str2double(y_str);

                        if ~isnan(x_val) && ~isnan(y_val)
                            x_coords = [x_coords; x_val];
                            y_coords = [y_coords; y_val];
                        end
                    end
                end

                fprintf('从表格中读取到 %d 个有效坐标点\n', length(x_coords));
            else
                msgbox('未找到标定表格', '错误', 'error');
                return;
            end

        elseif strcmp(choice, '上一步导出的EXCEL')
            % 文件选择对话框
            [filename, pathname] = uigetfile('像素数-坐标-波长-条纹编号_*.xlsx', '选择Excel文件');
            if filename == 0
                return;  % 用户取消
            end

            try
                % 读取Excel文件
                import_data = readmatrix(fullfile(pathname, filename));

                % 提取第2、3列数据（像素坐标X、Y）
                if size(import_data, 2) >= 3
                    x_coords = import_data(:, 2);
                    y_coords = import_data(:, 3);

                    % 过滤有效数据
                    valid_idx = ~isnan(x_coords) & ~isnan(y_coords);
                    x_coords = x_coords(valid_idx);
                    y_coords = y_coords(valid_idx);

                    fprintf('从文件 %s 中读取到 %d 个有效坐标点\n', filename, length(x_coords));
                else
                    msgbox('Excel文件格式不正确，需要至少3列数据', '错误', 'error');
                    return;
                end

            catch ME
                msgbox(['文件读取失败：' ME.message], '错误', 'error');
                return;
            end
        end

        % 检查是否有有效数据
        if isempty(x_coords) || isempty(y_coords)
            msgbox('未找到有效的坐标数据', '警告', 'warn');
            return;
        end

        % 直接创建第7步窗口
        fprintf('准备进入第7步，传递 %d 个坐标点\n', length(x_coords));

        % 获取img变量（从base workspace或其他方式）
        if evalin('base', 'exist(''img'', ''var'')')
            img = evalin('base', 'img');

            % 直接创建第7步旋转标定窗口
            rotation_window = createRotationCalibrationWindow(img, 'Absorption Spectrum', '吸收光谱', x_coords, y_coords);

            % 将rotation_window存储到base workspace，供后续步骤使用
            assignin('base', 'rotation_window', rotation_window);

            msgbox('已进入第7步旋转角度标定系统', '成功', 'help');
        else
            msgbox('无法获取图像数据，请重新运行程序', '错误', 'error');
        end

    catch ME
        msgbox(['操作失败：' ME.message], '错误', 'error');
    end
end

%% 内嵌函数：统一更新所有坐标显示
function updateAllCoordinates(src, ~, axes_lu, axes_ld, axes_ru, line1, wavelength, intensity, img, info_text_lu, info_text_ld, info_text_ru)
    % 获取当前鼠标位置
    current_point = get(gca, 'CurrentPoint');

    % 判断鼠标在哪个坐标轴上
    current_axes = gca;

    if current_axes == axes_lu
        % 左上角：实测强度图
        updateLineCoordinates(current_point, line1, info_text_lu, '像素位置', '相对强度');
    elseif current_axes == axes_ld
        % 左下角：CO2数据库图
        updateWavelengthCoordinates(current_point, wavelength, intensity, info_text_ld);
    elseif current_axes == axes_ru
        % 右上角：吸收光谱图
        updateImageCoordinates(current_point, img, info_text_ru, axes_ru);
    end
end

%% 内嵌函数：更新线图坐标显示
function updateLineCoordinates(current_point, data, info_text, x_label, y_label)
    x_pos = current_point(1, 1);
    y_pos = current_point(1, 2);

    % 获取坐标轴范围
    xlim_range = xlim;
    ylim_range = ylim;

    if x_pos >= xlim_range(1) && x_pos <= xlim_range(2) && ...
       y_pos >= ylim_range(1) && y_pos <= ylim_range(2)

        % 找到最近的数据点
        pixel_idx = round(x_pos);
        if pixel_idx >= 1 && pixel_idx <= length(data)
            intensity_val = data(pixel_idx);
            info_str = sprintf('%s: %d\n%s: %.3f', x_label, pixel_idx, y_label, intensity_val);
            set(info_text, 'String', info_str);
        else
            set(info_text, 'String', [x_label ': -\n' y_label ': -']);
        end
    else
        set(info_text, 'String', [x_label ': -\n' y_label ': -']);
    end
end

%% 内嵌函数：更新波长图坐标显示
function updateWavelengthCoordinates(current_point, wavelength, intensity, info_text)
    x_pos = current_point(1, 1);
    y_pos = current_point(1, 2);

    % 获取坐标轴范围
    xlim_range = xlim;
    ylim_range = ylim;

    if x_pos >= xlim_range(1) && x_pos <= xlim_range(2) && ...
       y_pos >= ylim_range(1) && y_pos <= ylim_range(2)

        % 找到最近的波长点
        [~, idx] = min(abs(wavelength - x_pos));
        if idx >= 1 && idx <= length(wavelength)
            wave_val = wavelength(idx);
            intensity_val = intensity(idx);
            info_str = sprintf('波长: %.6f\n相对强度: %.3e', wave_val, intensity_val);
            set(info_text, 'String', info_str);
        else
            set(info_text, 'String', '波长: -\n相对强度: -');
        end
    else
        set(info_text, 'String', '波长: -\n相对强度: -');
    end
end

%% 内嵌函数：更新图像坐标显示
function updateImageCoordinates(current_point, img, info_text, axes_ru)
    % 获取图像尺寸
    [img_height, img_width] = size(img);

    % 获取坐标轴范围
    xlim_range = get(axes_ru, 'XLim');
    ylim_range = get(axes_ru, 'YLim');

    % 获取鼠标在图像中的位置
    x_pos = current_point(1, 1);
    y_pos = current_point(1, 2);

    % 检查鼠标是否在图像范围内
    if x_pos >= xlim_range(1) && x_pos <= xlim_range(2) && ...
       y_pos >= ylim_range(1) && y_pos <= ylim_range(2)

        % 将坐标转换为像素坐标
        pixel_x = round(x_pos);
        pixel_y = round(y_pos);

        % 确保坐标在有效范围内
        if pixel_x >= 1 && pixel_x <= img_width && ...
           pixel_y >= 1 && pixel_y <= img_height

            % 获取像素值
            intensity = img(pixel_y, pixel_x);

            % 更新信息显示
            info_str = sprintf('像素坐标: (%d, %d)\n相对强度: %.3f', ...
                pixel_x, pixel_y, intensity);
            set(info_text, 'String', info_str);
        else
            set(info_text, 'String', '像素坐标: (-, -)\n相对强度: -');
        end
    else
        set(info_text, 'String', '像素坐标: (-, -)\n相对强度: -');
    end
end

%% 内嵌函数：为线图添加工具栏
function addPlotToolbar(parent_panel, target_axes)
    % 创建工具栏面板
    toolbar_panel = uipanel('Parent', parent_panel, ...
        'Position', [0.02, 0.92, 0.96, 0.06], ...
        'BorderType', 'none', ...
        'BackgroundColor', get(parent_panel, 'BackgroundColor'));

    % 缩放工具
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [5, 2, 30, 20], 'String', '🔍+', ...
        'FontSize', 8, 'ToolTip', '放大', ...
        'Callback', @(~,~) zoom(target_axes, 'on'));

    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [40, 2, 30, 20], 'String', '🔍-', ...
        'FontSize', 8, 'ToolTip', '缩小', ...
        'Callback', @(~,~) zoom(target_axes, 'out'));

    % 平移工具
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [75, 2, 30, 20], 'String', '👆', ...
        'FontSize', 8, 'ToolTip', '平移', ...
        'Callback', @(~,~) pan(target_axes, 'on'));

    % 数据光标
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [110, 2, 30, 20], 'String', '🎯', ...
        'FontSize', 8, 'ToolTip', '数据光标', ...
        'Callback', @(~,~) datacursormode(ancestor(target_axes, 'figure'), 'on'));

    % 重置视图
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [145, 2, 30, 20], 'String', '🏠', ...
        'FontSize', 8, 'ToolTip', '重置视图', ...
        'Callback', @(~,~) resetView(target_axes));

    % 网格开关
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [180, 2, 30, 20], 'String', '⊞', ...
        'FontSize', 8, 'ToolTip', '网格开关', ...
        'Callback', @(~,~) toggleGrid(target_axes));
end

%% 内嵌函数：为图像添加工具栏
function addImageToolbar(parent_panel, target_axes)
    % 创建工具栏面板
    toolbar_panel = uipanel('Parent', parent_panel, ...
        'Position', [0.02, 0.92, 0.96, 0.06], ...
        'BorderType', 'none', ...
        'BackgroundColor', get(parent_panel, 'BackgroundColor'));

    % 缩放工具
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [5, 2, 30, 20], 'String', '🔍+', ...
        'FontSize', 8, 'ToolTip', '放大', ...
        'Callback', @(~,~) zoom(target_axes, 'on'));

    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [40, 2, 30, 20], 'String', '🔍-', ...
        'FontSize', 8, 'ToolTip', '缩小', ...
        'Callback', @(~,~) zoom(target_axes, 'out'));

    % 平移工具
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [75, 2, 30, 20], 'String', '👆', ...
        'FontSize', 8, 'ToolTip', '平移', ...
        'Callback', @(~,~) pan(target_axes, 'on'));

    % 数据光标
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [110, 2, 30, 20], 'String', '🎯', ...
        'FontSize', 8, 'ToolTip', '数据光标', ...
        'Callback', @(~,~) datacursormode(ancestor(target_axes, 'figure'), 'on'));

    % 重置视图
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [145, 2, 30, 20], 'String', '🏠', ...
        'FontSize', 8, 'ToolTip', '重置视图', ...
        'Callback', @(~,~) resetImageView(target_axes));

    % 颜色映射切换
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [180, 2, 30, 20], 'String', '🎨', ...
        'FontSize', 8, 'ToolTip', '切换颜色映射', ...
        'Callback', @(~,~) toggleColormap(target_axes));

    % 颜色条开关
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [215, 2, 30, 20], 'String', '📊', ...
        'FontSize', 8, 'ToolTip', '颜色条开关', ...
        'Callback', @(~,~) toggleColorbar(target_axes));
end

%% 内嵌函数：重置线图视图
function resetView(target_axes)
    axis(target_axes, 'auto');
    zoom(target_axes, 'off');
    pan(target_axes, 'off');
    datacursormode(ancestor(target_axes, 'figure'), 'off');
end

%% 内嵌函数：重置图像视图
function resetImageView(target_axes)
    axis(target_axes, 'image');
    zoom(target_axes, 'off');
    pan(target_axes, 'off');
    datacursormode(ancestor(target_axes, 'figure'), 'off');
end

%% 内嵌函数：切换网格
function toggleGrid(target_axes)
    if strcmp(get(target_axes, 'XGrid'), 'on')
        grid(target_axes, 'off');
    else
        grid(target_axes, 'on');
    end
end

%% 内嵌函数：切换颜色映射
function toggleColormap(target_axes)
    current_colormap = colormap(target_axes);
    if isequal(current_colormap, jet(256))
        colormap(target_axes, hot);
    elseif isequal(current_colormap, hot(256))
        colormap(target_axes, gray);
    elseif isequal(current_colormap, gray(256))
        colormap(target_axes, parula);
    else
        colormap(target_axes, jet);
    end
end

%% 内嵌函数：切换颜色条
function toggleColorbar(target_axes)
    cb = colorbar(target_axes);
    if isempty(cb) || ~isvalid(cb)
        colorbar(target_axes);
    else
        colorbar(target_axes, 'off');
    end
end



%% 内嵌函数：设置像素图数据光标格式
function setupPixelDataCursor(target_axes)
    % 获取图形句柄
    fig = ancestor(target_axes, 'figure');

    % 获取数据光标管理器
    dcm = datacursormode(fig);

    % 设置自定义更新函数，传递坐标轴信息
    set(dcm, 'UpdateFcn', @(obj, event_obj) customDataCursorText(obj, event_obj, target_axes));
end

%% 内嵌函数：通用数据光标文本格式化
function txt = customDataCursorText(~, event_obj, target_axes)
    % 获取数据点位置
    pos = get(event_obj, 'Position');
    x_val = pos(1);
    y_val = pos(2);

    % 获取当前事件对象所在的坐标轴
    current_axes = get(event_obj, 'Target');
    current_axes = get(current_axes, 'Parent');

    % 获取坐标轴的标签来判断图形类型
    x_label = get(get(current_axes, 'XLabel'), 'String');

    % 根据X轴标签判断显示格式
    if contains(x_label, '像素')
        % 像素图：整数像素位置，3位小数强度
        txt = {sprintf('像素位置: %d', round(x_val)), ...
               sprintf('相对强度: %.3f', y_val)};
    elseif contains(x_label, '波长')
        % 波长图：6位小数波长，科学计数法强度
        txt = {sprintf('波长: %.6f', x_val), ...
               sprintf('相对强度: %.3e', y_val)};
    else
        % 默认格式
        txt = {sprintf('X: %.3f', x_val), ...
               sprintf('Y: %.3f', y_val)};
    end
end

%% 内嵌函数：导入旋转数据
function importRotationData(rotation_table)
    try
        % 选择文件
        [filename, pathname] = uigetfile('旋转特征点_*.xlsx', '选择要导入的旋转特征点文件');
        if filename == 0
            return;  % 用户取消
        end

        % 读取Excel文件
        import_data = readmatrix(fullfile(pathname, filename));

        % 转换为cell格式
        table_data = cell(50, 4);
        for i = 1:min(size(import_data, 1), 50)
            if i <= size(import_data, 1)
                table_data{i, 1} = num2str(import_data(i, 1));  % X1
                table_data{i, 2} = num2str(import_data(i, 2));  % Y1
                table_data{i, 3} = num2str(import_data(i, 3));  % X2
                table_data{i, 4} = num2str(import_data(i, 4));  % Y2
            else
                table_data{i, 1} = '';
                table_data{i, 2} = '';
                table_data{i, 3} = '';
                table_data{i, 4} = '';
            end
        end

        % 更新表格
        set(rotation_table, 'Data', table_data);
        msgbox(['数据已从 ' filename ' 导入成功！'], '导入成功', 'help');

    catch ME
        msgbox(['导入失败：' ME.message], '错误', 'error');
    end
end

%% 内嵌函数：导出旋转数据
function exportRotationData(rotation_table)
    try
        % 获取表格数据
        table_data = get(rotation_table, 'Data');

        % 收集完整的数据行（四列都有数值且不为0）
        export_data = [];
        for i = 1:size(table_data, 1)
            x1_str = table_data{i, 1};
            y1_str = table_data{i, 2};
            x2_str = table_data{i, 3};
            y2_str = table_data{i, 4};

            % 检查四列是否都有有效数值
            if ~isempty(x1_str) && ~isempty(y1_str) && ~isempty(x2_str) && ~isempty(y2_str) && ...
               ~strcmp(x1_str, '') && ~strcmp(y1_str, '') && ~strcmp(x2_str, '') && ~strcmp(y2_str, '')

                x1_val = str2double(x1_str);
                y1_val = str2double(y1_str);
                x2_val = str2double(x2_str);
                y2_val = str2double(y2_str);

                % 检查是否为有效数值且不为0
                if ~isnan(x1_val) && ~isnan(y1_val) && ~isnan(x2_val) && ~isnan(y2_val) && ...
                   x1_val ~= 0 && y1_val ~= 0 && x2_val ~= 0 && y2_val ~= 0
                    export_data = [export_data; x1_val, y1_val, x2_val, y2_val];
                end
            end
        end

        if isempty(export_data)
            msgbox('没有找到完整的数据行进行导出', '警告', 'warn');
            return;
        end

        % 生成文件名（自动递增序号）
        base_name = '旋转特征点';
        counter = 1;
        while exist([base_name '_' sprintf('%03d', counter) '.xlsx'], 'file')
            counter = counter + 1;
        end
        filename = [base_name '_' sprintf('%03d', counter) '.xlsx'];

        % 添加表头
        header = {'X1', 'Y1', 'X2', 'Y2'};

        % 导出到Excel
        writematrix(export_data, filename, 'Sheet', 1, 'Range', 'A2');
        writecell(header, filename, 'Sheet', 1, 'Range', 'A1');

        msgbox(['数据已导出到：' filename newline '共导出 ' num2str(size(export_data, 1)) ' 行完整数据'], '导出成功', 'help');

    catch ME
        msgbox(['导出失败：' ME.message], '错误', 'error');
    end
end

%% 内嵌函数：下一步操作（旋转标定）
function nextStepRotation()
    fprintf('用户点击了旋转标定的下一步按钮，准备进入第8步\n');

    % 直接调用第8步的核心功能
    executeStep8();
end

%% 内嵌函数：第9步核心功能函数
function executeStep9()
    % 清除第9步内部临时变量
    clear xi xi_rad Tx Ty T data_source choice;

    fprintf('开始第9步：坐标旋转校正...\n');

    % 第一个对话框：选择数据读取方式
    data_choice = questdlg('请选择原始波长特征点坐标读取方式？', '数据读取选择', ...
        '从第六步的右下角表格中读取', '从第六步导出的excel读取', '从第六步的右下角表格中读取');

    if isempty(data_choice)
        msgbox('操作取消', '提示', 'warn');
        return;
    end

    % 初始化数据变量
    data_source = [];

    switch data_choice
        case '从第六步的右下角表格中读取'
            % 从第6步表格中获取数据
            fprintf('正在从第6步表格中读取数据...\n');

            % 检查calibration_window变量是否存在
            window_exists = evalin('base', 'exist(''calibration_window'', ''var'')');
            if ~window_exists
                msgbox('无法访问第6步的标定窗口！请确保已完成第6步操作。', '错误', 'error');
                return;
            end

            % 从base workspace获取calibration_window
            calibration_window = evalin('base', 'calibration_window');

            % 检查窗口是否有效
            if ~isvalid(calibration_window)
                msgbox('第6步标定窗口已关闭或无效！', '错误', 'error');
                return;
            end

            % 查找窗口中的表格控件
            table_handle = findobj(calibration_window, 'Type', 'uitable');
            if isempty(table_handle)
                msgbox('无法找到第6步的标定表格！', '错误', 'error');
                return;
            end

            % 获取表格数据
            table_data = get(table_handle, 'Data');
            fprintf('表格数据大小: %d x %d\n', size(table_data, 1), size(table_data, 2));

            % 筛选完备数据：5列都有有效数值数据
            for i = 1:size(table_data, 1)
                pixel_num_str = table_data{i, 1};
                coord_x_str = table_data{i, 2};
                coord_y_str = table_data{i, 3};
                wavelength_str = table_data{i, 4};
                fringe_num_str = table_data{i, 5};

                % 检查5列是否都有有效数值
                if ~isempty(pixel_num_str) && ~isempty(coord_x_str) && ~isempty(coord_y_str) && ...
                   ~isempty(wavelength_str) && ~isempty(fringe_num_str) && ...
                   ~strcmp(pixel_num_str, '') && ~strcmp(coord_x_str, '') && ~strcmp(coord_y_str, '') && ...
                   ~strcmp(wavelength_str, '') && ~strcmp(fringe_num_str, '')

                    pixel_num_val = str2double(pixel_num_str);
                    coord_x_val = str2double(coord_x_str);
                    coord_y_val = str2double(coord_y_str);
                    wavelength_val = str2double(wavelength_str);
                    fringe_num_val = str2double(fringe_num_str);

                    % 检查是否为有效数值
                    if ~isnan(pixel_num_val) && ~isnan(coord_x_val) && ~isnan(coord_y_val) && ...
                       ~isnan(wavelength_val) && ~isnan(fringe_num_val)
                        data_source = [data_source; pixel_num_val, coord_x_val, coord_y_val, wavelength_val, fringe_num_val];
                    end
                end
            end

            if isempty(data_source)
                msgbox('表格中没有找到完备的数据行！', '错误', 'error');
                return;
            end

            fprintf('从表格中成功读取 %d 行完备数据\n', size(data_source, 1));

        case '从第六步导出的excel读取'
            % 弹出文件选择对话框
            [filename, pathname] = uigetfile('像素数-坐标-波长-条纹编号_*.xlsx', '选择第6步导出的Excel文件');
            if isequal(filename, 0)
                msgbox('未选择文件，操作取消', '提示', 'warn');
                return;
            end

            try
                data_source = readmatrix(fullfile(pathname, filename));
                fprintf('从Excel文件成功读取 %d 行数据\n', size(data_source, 1));

                % 过滤无效数据行
                valid_rows = [];
                for i = 1:size(data_source, 1)
                    if ~isnan(data_source(i, 1)) && ~isnan(data_source(i, 2)) && ~isnan(data_source(i, 3)) && ...
                       ~isnan(data_source(i, 4)) && ~isnan(data_source(i, 5))
                        valid_rows = [valid_rows; i];
                    end
                end

                if isempty(valid_rows)
                    msgbox('Excel文件中没有找到有效数据！', '错误', 'error');
                    return;
                end

                data_source = data_source(valid_rows, :);
                fprintf('过滤后剩余 %d 行有效数据\n', size(data_source, 1));

            catch ME
                msgbox(['读取Excel文件失败：' ME.message], '错误', 'error');
                return;
            end

        otherwise
            msgbox('操作取消', '提示', 'warn');
            return;
    end

    % 读取最优旋转角度
    try
        xi = readmatrix('best_xi.xlsx');
        xi = xi(1, 1);  % 取第一行第一列
        xi_rad = xi/180*pi;  % 转换为弧度
        fprintf('成功读取最优旋转角度: %.6f 度\n', xi);
    catch ME
        msgbox(['读取最优角度失败：' ME.message], '错误', 'error');
        return;
    end

    % 读取旋转中心
    try
        if evalin('base', 'exist(''rotation_center_Tx'', ''var'')')
            Tx = evalin('base', 'rotation_center_Tx');
        else
            msgbox('无法获取旋转中心Tx！', '错误', 'error');
            return;
        end

        if evalin('base', 'exist(''rotation_center_Ty'', ''var'')')
            Ty = evalin('base', 'rotation_center_Ty');
        else
            msgbox('无法获取旋转中心Ty！', '错误', 'error');
            return;
        end

        fprintf('旋转中心: Tx = %.1f, Ty = %.1f\n', Tx, Ty);
    catch ME
        msgbox(['读取旋转中心失败：' ME.message], '错误', 'error');
        return;
    end

    % 构建旋转变换矩阵
    T = [cos(xi_rad) sin(xi_rad) Tx*cos(xi_rad)+Ty*sin(xi_rad)-Tx
        -sin(xi_rad) cos(xi_rad) -Tx*sin(xi_rad)+Ty*cos(xi_rad)-Ty
        0 0 1];

    fprintf('开始坐标旋转变换...\n');

    % 对每行数据进行坐标变换
    for i = 1:size(data_source, 1)
        % 原坐标 (第2、3列)
        p = [data_source(i, 2); data_source(i, 3); 1];

        % 旋转变换
        p_rotated = T * p;

        % 更新坐标 (第2、3列)
        data_source(i, 2) = p_rotated(1);
        data_source(i, 3) = p_rotated(2);
        % 第1、4、5列保持不变
    end

    % 保存结果到Excel文件
    try
        output_filename = '波长点旋转后.xlsx';

        % 删除旧文件（如果存在）
        if exist(output_filename, 'file')
            delete(output_filename);
            fprintf('已删除旧的输出文件\n');
        end

        % 添加表头
        header = {'像素数', '旋转后像素坐标X', '旋转后像素坐标Y', '波长值', '所在条纹编号'};

        % 保存数据
        writematrix(data_source, output_filename, 'Sheet', 1, 'Range', 'A2');
        writecell(header, output_filename, 'Sheet', 1, 'Range', 'A1');

        fprintf('坐标旋转校正完成！\n');
        fprintf('处理了 %d 个波长标定点\n', size(data_source, 1));
        fprintf('结果已保存到文件: %s\n', output_filename);

        % 显示完成信息
        msgbox(sprintf('坐标旋转校正完成！\n处理了 %d 个波长标定点\n结果已保存到: %s', ...
            size(data_source, 1), output_filename), '处理完成', 'help');

    catch ME
        msgbox(['保存结果失败：' ME.message], '错误', 'error');
        return;
    end

    % 第9步完成后自动进入第10步
    fprintf('第9步完成，自动进入第10步...\n');
    executeStep10();
end

%% 内嵌函数：第10步核心功能函数
function executeStep10()
    % 清除第10步内部临时变量
    clear data_fit m_best abc_coeff k error y x P xi yi;

    fprintf('开始第10步：迭代求解 m A B C...\n');

    % 读取第9步输出的数据
    try
        data_fit = readmatrix('波长点旋转后.xlsx');
        fprintf('成功读取波长点旋转后数据，共 %d 个数据点\n', size(data_fit, 1));
    catch ME
        fprintf('读取波长点旋转后.xlsx失败：%s\n', ME.message);
        return;
    end

    fprintf('开始迭代求解最优m值（范围：3300-3600）...\n');

    % 迭代求解最优m值
    k = 1;
    error = zeros(101, 1);  % 预分配数组

    for m = 3300:1:3600
        % 计算修正波长值 Mλ = λ×(m-Δm)
        for i = 1:length(data_fit)
            y(i,1) = data_fit(i,4)*(m - data_fit(i,5));
        end

        % 二次多项式拟合：Mλ = A×Y² + B×Y + C
        x = data_fit(:,3);  % Y坐标
        P = polyfit(x,y,2);

        % 计算拟合误差
        error(k,1) = sum(abs(y-polyval(P,x)));
        k = k + 1;
    end

    % 找到最优m值
    [~,b] = min(error);
    m_best = 3300 + b-1;

    fprintf('找到最优m值: %d\n', m_best);

    % 用最优m重新拟合得到最终ABC系数
    for i = 1:length(data_fit)
        y(i,1) = data_fit(i,4)*(m_best - data_fit(i,5));
    end
    x = data_fit(:,3);
    abc_coeff = polyfit(x,y,2);  % [A, B, C]

    % 生成完整拟合曲线
    xi = 141:420;  % 硬编码Y坐标范围
    xi = xi';
    yi = polyval(abc_coeff, xi);

    % 结果输出到命令行
    fprintf('\n第10步：迭代求解完成！\n');
    fprintf('最优m值: %d\n', m_best);
    fprintf('拟合系数A: %.6f\n', abc_coeff(1));
    fprintf('拟合系数B: %.6f\n', abc_coeff(2));
    fprintf('拟合系数C: %.6f\n', abc_coeff(3));

    % 保存m值到Excel文件
    try
        % 删除旧文件（如果存在）
        if exist('m.xlsx', 'file')
            delete('m.xlsx');
        end

        writematrix(m_best, 'm.xlsx');
        fprintf('m值已保存到文件: m.xlsx\n');
    catch ME
        fprintf('保存m值失败：%s\n', ME.message);
    end

    % 将abc_coeff和绘图数据保存到base workspace供后续使用
    assignin('base', 'abc_coeff', abc_coeff);
    assignin('base', 'm_best', m_best);
    assignin('base', 'error_array', error);
    assignin('base', 'data_fit_for_plot', data_fit);
    assignin('base', 'm_range', 3300:1:3600);

    fprintf('第10步完成！ABC系数已保存到变量abc_coeff中\n');

    % 第10步完成后自动进入第11步
    fprintf('第10步完成，自动进入第11步...\n');
    executeStep11();
end

%% 内嵌函数：第11步核心功能函数
function executeStep11()
    % 清除第11步内部临时变量
    clear data_fit m_best abc_coeff xi_rad Tx Ty T;
    clear x_stripe fsr_height lambda_matrix output_matrix;
    clear all_wavelengths expected_range lambda_table;

    fprintf('开始第11步：波长重构...\n');

    % 读取第10步的标定参数
    try
        % 读取最优m值
        m_best = readmatrix('m.xlsx');
        m_best = m_best(1, 1);
        fprintf('成功读取最优m值: %d\n', m_best);

        % 从workspace读取ABC系数
        if evalin('base', 'exist(''abc_coeff'', ''var'')')
            abc_coeff = evalin('base', 'abc_coeff');
            fprintf('成功读取ABC系数: A=%.6f, B=%.6f, C=%.6f\n', abc_coeff(1), abc_coeff(2), abc_coeff(3));
        else
            error('无法获取ABC系数');
        end

        % 读取旋转角度和旋转中心
        if evalin('base', 'exist(''best_xi'', ''var'')')
            xi = evalin('base', 'best_xi');
            xi_rad = xi/180*pi;  % 转换为弧度
        else
            error('无法获取旋转角度');
        end

        if evalin('base', 'exist(''rotation_center_Tx'', ''var'')') && evalin('base', 'exist(''rotation_center_Ty'', ''var'')')
            Tx = evalin('base', 'rotation_center_Tx');
            Ty = evalin('base', 'rotation_center_Ty');
        else
            error('无法获取旋转中心');
        end

        % 构建旋转变换矩阵
        T = [cos(xi_rad) sin(xi_rad) Tx*cos(xi_rad)+Ty*sin(xi_rad)-Tx
            -sin(xi_rad) cos(xi_rad) -Tx*sin(xi_rad)+Ty*cos(xi_rad)-Ty
            0 0 1];

        % 读取FSR参数和条纹位置
        if evalin('base', 'exist(''fsr_values'', ''var'')')
            fsr_values = evalin('base', 'fsr_values');
            high = fsr_values.high;
            low = fsr_values.low;
        else
            error('无法获取FSR参数');
        end

        if evalin('base', 'exist(''loc'', ''var'')')
            loc = evalin('base', 'loc');
        else
            error('无法获取条纹位置信息');
        end

    catch ME
        fprintf('读取标定参数失败：%s\n', ME.message);
        return;
    end

    fprintf('开始计算整个FSR区域的波长分布...\n');

    % 计算条纹X坐标 - 取每个条纹中间Y位置的X坐标
    x_stripe = round(loc(ceil(size(loc,1)/2), :));

    % 计算FSR区域参数
    fsr_height = high - low + 1;  % FSR区域的高度（像素数）
    num_stripes = length(x_stripe);  % 条纹数量

    fprintf('FSR区域高度: %d 像素\n', fsr_height);
    fprintf('检测到条纹数量: %d\n', num_stripes);

    % 预分配存储数组
    % lambda_matrix: 3维数组 [高度, 信息类型, 条纹编号]
    % 信息类型: 1=波长值, 2=X坐标, 3=Y坐标
    lambda_matrix = zeros(fsr_height, 3, num_stripes);

    % output_matrix: 2维输出数组 [高度, Y坐标+各条纹波长]
    output_matrix = zeros(fsr_height, num_stripes + 1);

    % 遍历每个条纹进行波长计算
    for stripe_idx = 1:num_stripes
        % 只显示第1条、第2条和最后一条的进度
        if stripe_idx == 1 || stripe_idx == 2 || stripe_idx == num_stripes
            fprintf('正在处理第 %d/%d 条条纹...\n', stripe_idx, num_stripes);
        elseif stripe_idx == 3
            fprintf('...\n');
        end

        % 遍历FSR区域的每一行像素
        for row_idx = 1:fsr_height
            actual_y = low + row_idx - 1;  % 实际的Y坐标（在原图像中的位置）

            % 构建原始像素坐标 [X, Y, 1]
            original_coord = [x_stripe(stripe_idx); actual_y; 1];

            % 应用旋转变换得到校正后的坐标
            rotated_coord = T * original_coord;
            rotated_y = rotated_coord(2);  % 旋转后的Y坐标

            % 使用多项式计算修正波长值 Mλ = A*Y² + B*Y + C
            corrected_wavelength = polyval(abc_coeff, rotated_y);

            % 计算实际干涉级次 M = m_best - 条纹编号
            actual_interference_order = m_best - stripe_idx;

            % 计算真实波长值 λ = Mλ / M
            true_wavelength = corrected_wavelength / actual_interference_order;

            % 存储到3维详细数组
            lambda_matrix(row_idx, 1, stripe_idx) = true_wavelength;  % 波长值
            lambda_matrix(row_idx, 2, stripe_idx) = x_stripe(stripe_idx);  % X坐标
            lambda_matrix(row_idx, 3, stripe_idx) = actual_y;  % Y坐标

            % 存储到2维输出数组
            output_matrix(row_idx, 1) = actual_y;  % Y坐标（第1列）
            output_matrix(row_idx, stripe_idx + 1) = true_wavelength;  % 波长值
        end
    end

    fprintf('波长计算完成！\n');

    % 计算全局波长统计信息
    all_wavelengths = lambda_matrix(:, 1, :);  % 提取所有波长值
    all_wavelengths = all_wavelengths(:);  % 转换为一维数组
    all_wavelengths = all_wavelengths(all_wavelengths > 0);  % 去除零值

    % 波长合理性检查
    expected_range = [1400, 1470];  % 预期的波长范围(nm)
    min_wavelength = min(all_wavelengths);
    max_wavelength = max(all_wavelengths);

    fprintf('\n=== 波长统计信息 ===\n');
    fprintf('计算的波长范围: %.3f - %.3f nm\n', min_wavelength, max_wavelength);
    fprintf('平均波长: %.3f nm\n', mean(all_wavelengths));
    fprintf('波长标准差: %.3f nm\n', std(all_wavelengths));

    % 检查波长的合理性
    if min_wavelength >= expected_range(1) && max_wavelength <= expected_range(2)
        fprintf('✅ 波长范围检查: 通过 (在预期范围 %d-%d nm 内)\n', expected_range(1), expected_range(2));
    else
        fprintf('⚠️  波长范围检查: 异常 (超出预期范围 %d-%d nm)\n', expected_range(1), expected_range(2));
        if min_wavelength < expected_range(1)
            fprintf('   最小波长 %.3f nm 小于预期下限 %d nm\n', min_wavelength, expected_range(1));
        end
        if max_wavelength > expected_range(2)
            fprintf('   最大波长 %.3f nm 大于预期上限 %d nm\n', max_wavelength, expected_range(2));
        end
    end

    % 保存主要波长输出结果
    try
        output_filename = '表格波长.xlsx';
        if exist(output_filename, 'file')
            delete(output_filename);
        end

        % 创建表头 - 第一列空着，其他列用条纹编号
        header = cell(1, num_stripes + 1);
        header{1} = '';  % 第一列空着
        for i = 1:num_stripes
            header{i+1} = num2str(i);  % 条纹编号：1, 2, 3, 4...
        end

        % 保存数据
        writematrix(output_matrix, output_filename, 'Sheet', 1, 'Range', 'A2');
        writecell(header, output_filename, 'Sheet', 1, 'Range', 'A1');

        fprintf('主要波长数据已保存到: %s\n', output_filename);

    catch ME
        fprintf('保存主要结果失败：%s\n', ME.message);
    end

    % 保存详细的像素-波长映射表
    try
        % 读取第3步的一维光谱数据（与第12步完全一致的强度数据）
        intensity_data = [];
        line_g_data = [];
        loc_data = [];
        try
            if evalin('base', 'exist(''line1'', ''var'')')
                intensity_data = evalin('base', 'line1');
                fprintf('成功读取第3步一维光谱数据line1，共 %d 个数据点\n', length(intensity_data));
            else
                error('无法获取第3步一维光谱数据line1');
            end

            if evalin('base', 'exist(''line_g'', ''var'')')
                line_g_data = evalin('base', 'line_g');
                fprintf('成功读取条纹位置信息line_g\n');
            else
                error('无法获取条纹位置信息line_g');
            end

            if evalin('base', 'exist(''loc'', ''var'')')
                loc_data = evalin('base', 'loc');
                [~, fringe_count] = size(loc_data);
                fprintf('成功读取条纹中心位置信息，共 %d 条条纹\n', fringe_count);
            else
                error('无法获取条纹中心位置信息');
            end
        catch ME
            fprintf('⚠️  无法读取第3步数据：%s\n', ME.message);
            fprintf('将使用默认强度值\n');
            % 设置默认值，避免后续代码出错
            intensity_data = [];
            line_g_data = [];
            fringe_count = size(lambda_matrix, 3);  % 从lambda_matrix获取条纹数
        end

        % 检查是否有有效的强度数据
        if isempty(intensity_data)
            fprintf('⚠️  没有有效的强度数据，将使用默认值\n');
            % 使用原来的方法（从Excel文件读取）
            try
                pixel_data_file = readmatrix('像素数-强度-坐标-所在条纹编号.xlsx');
                intensity_data = pixel_data_file(:, 2);  % 第2列是强度
                fprintf('成功读取Excel强度数据，共 %d 个数据点\n', length(intensity_data));
            catch
                fprintf('⚠️  也无法读取Excel强度数据，将使用零值\n');
                intensity_data = zeros(size(lambda_matrix, 1) * size(lambda_matrix, 3), 1);
            end
        end

        % 构建与第12步完全一致的波长-强度映射
        fprintf('开始构建与第12步一致的波长-强度映射...\n');

        % 计算每个条纹的像素数
        if ~isempty(intensity_data) && fringe_count > 0
            pixels_per_fringe = length(intensity_data) / fringe_count;
        else
            pixels_per_fringe = size(lambda_matrix, 1);  % 默认值
        end

        % 将3维数组重新整理为2维表格格式，使用第3步的强度数据
        lambda_table = [];

        % 根据数据可用性选择不同的处理方式
        if ~isempty(line_g_data)
            % 方式1：使用第3步的像素顺序（推荐，与第12步完全一致）
            fprintf('使用第3步像素顺序构建数据表...\n');
            for pixel_idx = 1:length(intensity_data)
                try
                    % 计算对应的条纹编号（更严格的边界检查）
                    stripe_num = ceil(pixel_idx / pixels_per_fringe);

                    % 确保条纹编号在有效范围内
                    if stripe_num < 1 || stripe_num > fringe_count
                        continue;
                    end

                    % 获取对应的Y坐标（从line_g数据）
                    if pixel_idx > size(line_g_data, 1)
                        continue;  % 防止越界
                    end

                    y_coord = line_g_data(pixel_idx, 2) + fsr_values.low - 1;
                    x_coord = line_g_data(pixel_idx, 3);

                    % 转换为lambda_matrix的行索引
                    row_idx = y_coord - fsr_values.low + 1;

                    % 更严格的边界检查
                    if row_idx >= 1 && row_idx <= size(lambda_matrix, 1) && ...
                       stripe_num >= 1 && stripe_num <= size(lambda_matrix, 3) && ...
                       y_coord >= fsr_values.low && y_coord <= fsr_values.high

                        wavelength_val = lambda_matrix(row_idx, 1, stripe_num);

                        if wavelength_val > 0 && isfinite(wavelength_val)  % 更严格的有效性检查
                            lambda_table = [lambda_table; ...
                                stripe_num, ...           % 条纹编号
                                y_coord, ...              % Y坐标
                                x_coord, ...              % X坐标
                                wavelength_val, ...       % 波长值
                                intensity_data(pixel_idx)]; % 第3步的强度值
                        end
                    end
                catch ME
                    % 异常情况跳过，但记录错误信息
                    if pixel_idx <= 10  % 只记录前10个错误，避免刷屏
                        fprintf('像素 %d 处理异常: %s\n', pixel_idx, ME.message);
                    end
                    continue;
                end
            end
        else
            % 方式2：使用原来的方法（按条纹和Y坐标遍历）
            fprintf('使用原来的条纹遍历方法构建数据表...\n');
            data_index = 1;  % 用于匹配强度数据的索引

            for stripe = 1:size(lambda_matrix, 3)
                for y_idx = 1:size(lambda_matrix, 1)
                    if lambda_matrix(y_idx, 1, stripe) > 0  % 只保存有效波长值
                        % 获取对应的强度值
                        intensity_value = 0;  % 默认值
                        if ~isempty(intensity_data) && data_index <= length(intensity_data)
                            intensity_value = intensity_data(data_index);
                        end

                        lambda_table = [lambda_table; ...
                            stripe, ...                           % 条纹编号
                            lambda_matrix(y_idx, 3, stripe), ... % Y坐标
                            lambda_matrix(y_idx, 2, stripe), ... % X坐标
                            lambda_matrix(y_idx, 1, stripe), ... % 波长值
                            intensity_value];                     % 强度值
                    end
                    data_index = data_index + 1;  % 递增索引
                end
            end
        end

        % 创建表格并保存
        detailed_filename = '逐行波长.xlsx';
        if exist(detailed_filename, 'file')
            delete(detailed_filename);
        end

        lambda_excel_table = array2table(lambda_table, ...
            'VariableNames', {'StripeNumber', 'Y_Coordinate', 'X_Coordinate', 'Wavelength_nm', 'Intensity'});
        writetable(lambda_excel_table, detailed_filename);

        fprintf('详细波长映射表已保存到: %s\n', detailed_filename);
        fprintf('详细映射表包含 %d 个有效像素点\n', size(lambda_table, 1));

        % 创建按波长升序排列的表格并保存
        sorted_filename = '逐行波长_升序_含强度.xlsx';
        if exist(sorted_filename, 'file')
            delete(sorted_filename);
        end

        % 按第4列（波长值）进行升序排序
        [~, sort_idx] = sort(lambda_table(:, 4));
        lambda_table_sorted = lambda_table(sort_idx, :);

        lambda_sorted_table = array2table(lambda_table_sorted, ...
            'VariableNames', {'StripeNumber', 'Y_Coordinate', 'X_Coordinate', 'Wavelength_nm', 'Intensity'});
        writetable(lambda_sorted_table, sorted_filename);

        fprintf('波长升序映射表已保存到: %s\n', sorted_filename);
        fprintf('✅ 强度数据来源: 第3步优化的一维光谱数据（与第12步图像完全一致）\n');

    catch ME
        fprintf('保存详细映射表失败：%s\n', ME.message);
    end

    % 将关键结果保存到workspace供后续使用
    assignin('base', 'lambda_matrix', lambda_matrix);
    assignin('base', 'output_matrix', output_matrix);
    assignin('base', 'wavelength_range', [min_wavelength, max_wavelength]);

    fprintf('\n第11步完成！波长重构已完成\n');
    fprintf('=== 处理总结 ===\n');
    fprintf('处理的FSR区域: %d x %d 像素\n', fsr_height, num_stripes);
    fprintf('生成的波长点数: %d\n', sum(all_wavelengths > 0));
    fprintf('波长计算范围: %.3f - %.3f nm\n', min_wavelength, max_wavelength);

    % 第11步完成后自动进入第12步
    fprintf('第11步完成，自动进入第12步...\n');
    executeStep12();
end

%% 内嵌函数：第12步核心功能函数
function executeStep12()
    % 清除第12步内部临时变量
    clear wavelength_axis pixels_per_fringe;

    fprintf('开始第12步：一维谱图波长标定显示...\n');

    % 读取必要数据
    try
        % 从workspace读取第3步的一维谱图数据
        if evalin('base', 'exist(''line1'', ''var'')')
            line1 = evalin('base', 'line1');
            fprintf('成功读取一维谱图数据，共 %d 个数据点\n', length(line1));
        else
            error('无法获取一维谱图数据line1');
        end

        if evalin('base', 'exist(''line_g'', ''var'')')
            line_g = evalin('base', 'line_g');
        else
            error('无法获取条纹坐标数据line_g');
        end

        % 读取FSR参数
        if evalin('base', 'exist(''fsr_values'', ''var'')')
            fsr_values = evalin('base', 'fsr_values');
        else
            error('无法获取FSR参数');
        end

        % 读取第11步的波长重构结果
        if evalin('base', 'exist(''lambda_matrix'', ''var'')')
            lambda_matrix = evalin('base', 'lambda_matrix');
            fprintf('成功读取波长重构数据，尺寸: %dx%dx%d\n', size(lambda_matrix));
        else
            error('无法获取波长重构数据lambda_matrix');
        end

        % 读取条纹数量信息
        if evalin('base', 'exist(''loc'', ''var'')')
            loc = evalin('base', 'loc');
            [~, fringe_count] = size(loc);
        else
            error('无法获取条纹位置信息');
        end

    catch ME
        fprintf('读取数据失败：%s\n', ME.message);
        return;
    end

    fprintf('开始构建波长轴...\n');

    % 计算每个条纹的像素数
    pixels_per_fringe = length(line1) / fringe_count;

    % 构建波长轴 - "狸猫换太子"
    wavelength_axis = zeros(size(line1));

    for i = 1:length(line1)
        try
            % 计算对应的条纹编号（更严格的边界检查）
            stripe_num = ceil(i / pixels_per_fringe);

            % 确保条纹编号在有效范围内
            if stripe_num < 1 || stripe_num > fringe_count
                if i > 1
                    wavelength_axis(i) = wavelength_axis(i-1);
                end
                continue;
            end

            % 检查line_g数组边界
            if i > size(line_g, 1)
                if i > 1
                    wavelength_axis(i) = wavelength_axis(i-1);
                end
                continue;
            end

            % 获取对应的Y坐标
            y_coord = line_g(i, 2) + fsr_values.low - 1;

            % 转换为lambda_matrix的行索引
            row_idx = y_coord - fsr_values.low + 1;

            % 更严格的边界检查
            if row_idx >= 1 && row_idx <= size(lambda_matrix, 1) && ...
               stripe_num >= 1 && stripe_num <= size(lambda_matrix, 3) && ...
               y_coord >= fsr_values.low && y_coord <= fsr_values.high

                wavelength_val = lambda_matrix(row_idx, 1, stripe_num);

                % 检查波长值的有效性
                if wavelength_val > 0 && isfinite(wavelength_val)
                    wavelength_axis(i) = wavelength_val;
                else
                    % 无效波长值，使用邻近值
                    if i > 1
                        wavelength_axis(i) = wavelength_axis(i-1);
                    end
                end
            else
                % 边界情况，使用邻近值或插值
                if i > 1
                    wavelength_axis(i) = wavelength_axis(i-1);
                end
            end

        catch ME
            % 异常情况处理
            if i <= 10  % 只记录前10个错误
                fprintf('波长轴构建异常 (像素 %d): %s\n', i, ME.message);
            end
            if i > 1
                wavelength_axis(i) = wavelength_axis(i-1);
            end
        end
    end

    % 检查波长轴的有效性
    valid_wavelengths = wavelength_axis(wavelength_axis > 0);
    if length(valid_wavelengths) < length(line1) * 0.8
        fprintf('⚠️  警告：有效波长点数较少 (%d/%d)\n', length(valid_wavelengths), length(line1));
    end

    fprintf('波长轴构建完成！\n');
    fprintf('波长范围: %.6f - %.6f nm\n', min(valid_wavelengths), max(valid_wavelengths));

    % 绘制波长标定的一维谱图
    try
        figure('Name', 'VIPA波长标定谱图', 'NumberTitle', 'off', 'Position', [200, 200, 800, 600]);

        % 狸猫换太子 - 保持原图形状，只换X轴
        plot(wavelength_axis, line1, '.-', 'LineWidth', 1.5, 'MarkerSize', 6, 'Color', [0 0.4470 0.7410]);

        % 设置图形属性
        xlabel('波长 (nm)', 'FontSize', 12, 'FontName', 'SimHei');
        ylabel('相对强度', 'FontSize', 12, 'FontName', 'SimHei');
        title('一维谱图-波长标定版', 'FontSize', 14, 'FontName', 'SimHei');
        grid on;
        grid minor;

        % 设置数据提示格式，波长显示6位小数
        dcm = datacursormode(gcf);
        set(dcm, 'UpdateFcn', @(obj, event_obj) customDataTip(obj, event_obj));

        % 设置X轴范围
        xlim([min(valid_wavelengths), max(valid_wavelengths)]);

        fprintf('波长标定谱图绘制完成！\n');

    catch ME
        fprintf('绘图失败：%s\n', ME.message);
    end



    % 将波长轴保存到workspace
    assignin('base', 'wavelength_axis', wavelength_axis);

    fprintf('\n第12步完成！一维谱图波长标定显示完成\n');
    fprintf('=== 处理总结 ===\n');
    fprintf('原始数据点数: %d\n', length(line1));
    fprintf('有效波长点数: %d\n', length(valid_wavelengths));
    fprintf('波长标定范围: %.3f - %.3f nm\n', min(valid_wavelengths), max(valid_wavelengths));
    fprintf('图形形状: 保持原始一维谱图形状\n');
    fprintf('X轴标签: 从像素位置更换为波长(nm)\n');

    % 第12步完成后自动进入第13步
    fprintf('第12步完成，自动进入第13步...\n');
    executeStep13();
end

%% 内嵌函数：第8步核心功能函数
function executeStep8()
    % 清除可能的缓存影响
    clear p_13 best_xi error_values;

    % 第一个对话框：选择数据导入方式
    data_choice = questdlg('选择您的导入方式？', '数据导入选择', ...
        '上一步表格中', '上一步导出的EXCEL', '上一步表格中');

    switch data_choice
        case '上一步表格中'
            % 从第7步表格中获取数据
            fprintf('正在检查rotation_window变量...\n');

            % 检查变量是否存在
            window_exists = evalin('base', 'exist(''rotation_window'', ''var'')');
            fprintf('rotation_window变量存在状态: %d\n', window_exists);

            if window_exists
                % 从base workspace获取rotation_window
                rotation_window = evalin('base', 'rotation_window');
                fprintf('成功获取rotation_window变量\n');

                % 检查窗口是否有效
                if isvalid(rotation_window)
                    fprintf('rotation_window窗口有效\n');

                    % 查找窗口中的表格控件
                    table_handle = findobj(rotation_window, 'Type', 'uitable');
                    fprintf('找到的表格控件数量: %d\n', length(table_handle));

                    if isempty(table_handle)
                        msgbox('无法找到表格控件！', '错误', 'error');
                        return;
                    end

                    table_data = get(table_handle, 'Data');
                    fprintf('表格数据大小: %d x %d\n', size(table_data, 1), size(table_data, 2));

                    % 筛选完备数据：4列都有非零非空的数值数据（与导出逻辑完全一致）
                    p_13 = [];
                    for i = 1:size(table_data, 1)
                        x1_str = table_data{i, 1};
                        y1_str = table_data{i, 2};
                        x2_str = table_data{i, 3};
                        y2_str = table_data{i, 4};

                        % 检查四列是否都有有效数值（与导出逻辑完全一致）
                        if ~isempty(x1_str) && ~isempty(y1_str) && ~isempty(x2_str) && ~isempty(y2_str) && ...
                           ~strcmp(x1_str, '') && ~strcmp(y1_str, '') && ~strcmp(x2_str, '') && ~strcmp(y2_str, '')

                            x1_val = str2double(x1_str);
                            y1_val = str2double(y1_str);
                            x2_val = str2double(x2_str);
                            y2_val = str2double(y2_str);

                            % 检查是否为有效数值且不为0（与导出逻辑完全一致）
                            if ~isnan(x1_val) && ~isnan(y1_val) && ~isnan(x2_val) && ~isnan(y2_val) && ...
                               x1_val ~= 0 && y1_val ~= 0 && x2_val ~= 0 && y2_val ~= 0
                                p_13 = [p_13; x1_val, y1_val, x2_val, y2_val];
                            end
                        end
                    end

                    if isempty(p_13)
                        msgbox('表格中没有找到完备的数据行！', '错误', 'error');
                        return;
                    end

                    fprintf('从表格中成功读取 %d 行完备数据\n', size(p_13, 1));
                else
                    msgbox('第7步窗口已关闭或无效！', '错误', 'error');
                    return;
                end
            else
                msgbox('无法访问第7步的表格数据！请确保已完成第7步操作。', '错误', 'error');
                return;
            end

        case '上一步导出的EXCEL'
            % 弹出文件选择对话框
            [filename, pathname] = uigetfile('*.xlsx', '选择第7步导出的Excel文件');
            if isequal(filename, 0)
                msgbox('未选择文件，操作取消', '提示', 'warn');
                return;
            end

            try
                p_13 = readmatrix(fullfile(pathname, filename));
                fprintf('从Excel文件成功读取 %d 行数据\n', size(p_13, 1));
            catch ME
                msgbox(['读取Excel文件失败：' ME.message], '错误', 'error');
                return;
            end

        otherwise
            msgbox('操作取消', '提示', 'warn');
            return;
    end

    % 第二个对话框：选择旋转中心获取方式
    center_choice = questdlg('请选择旋转中心获取方式？', '旋转中心选择', ...
        '默认相片尺寸一半', '手动输入', '默认相片尺寸一半');

    switch center_choice
        case '默认相片尺寸一半'
            % 获取图片尺寸
            if evalin('base', 'exist(''img'', ''var'')')
                img = evalin('base', 'img');
                [height, width] = size(img);
                Tx = width / 2;
                Ty = height / 2;
                fprintf('使用默认旋转中心：Tx = %.1f, Ty = %.1f\n', Tx, Ty);
            else
                msgbox('无法获取图像尺寸！', '错误', 'error');
                return;
            end

        case '手动输入'
            % 创建输入对话框
            prompt = {'请输入Tx（横坐标旋转中心）:', '请输入Ty（纵坐标旋转中心）:'};
            dlgtitle = '手动输入旋转中心';
            dims = [1 35];
            definput = {'320', '256'};  % 默认值

            answer = inputdlg(prompt, dlgtitle, dims, definput);
            if isempty(answer)
                msgbox('操作取消', '提示', 'warn');
                return;
            end

            try
                Tx = str2double(answer{1});
                Ty = str2double(answer{2});

                if isnan(Tx) || isnan(Ty)
                    msgbox('输入的数值无效！', '错误', 'error');
                    return;
                end

                fprintf('使用手动输入的旋转中心：Tx = %.1f, Ty = %.1f\n', Tx, Ty);
            catch
                msgbox('输入格式错误！', '错误', 'error');
                return;
            end

        otherwise
            msgbox('操作取消', '提示', 'warn');
            return;
    end

    % 执行昊哥的旋转角度优化算法
    fprintf('开始计算最优旋转角度...\n');

    % 两阶段高精度角度优化
    fprintf('第一阶段：粗搜索...\n');

    % 第一阶段：粗搜索 - 0.01度步长
    coarse_angles = -2.3:0.01:-1.7;
    coarse_errors = zeros(length(coarse_angles), 1);

    for k = 1:length(coarse_angles)
        angle_deg = coarse_angles(k);
        error_val = calculateRotationError(angle_deg, p_13, Tx, Ty);
        coarse_errors(k) = error_val;
    end

    % 找到粗搜索的最优角度
    [~, coarse_min_idx] = min(coarse_errors);
    coarse_best = coarse_angles(coarse_min_idx);

    fprintf('第二阶段：精细搜索...\n');

    % 第二阶段：在最优角度附近进行精细搜索 - 0.000001度步长
    fine_range = 0.02;  % 在最优角度±0.02度范围内精细搜索
    fine_step = 0.000001;  % 1微度精度
    fine_angles = (coarse_best - fine_range):fine_step:(coarse_best + fine_range);
    fine_errors = zeros(length(fine_angles), 1);

    for k = 1:length(fine_angles)
        angle_deg = fine_angles(k);
        error_val = calculateRotationError(angle_deg, p_13, Tx, Ty);
        fine_errors(k) = error_val;
    end

    % 找到精细搜索的最优角度
    [min_error, fine_min_idx] = min(fine_errors);
    best_xi = fine_angles(fine_min_idx);  % 最优旋转角度（度）- 真正的6位小数精度

    fprintf('计算完成！\n');
    fprintf('最优旋转角度: %.6f 度\n', best_xi);
    fprintf('对应的最小误差: %.6f\n', min_error);

    % 输出到Excel文件
    try
        % 删除旧文件（如果存在）
        if exist('best_xi.xlsx', 'file')
            delete('best_xi.xlsx');
        end

        % 保存新文件
        writematrix(best_xi, 'best_xi.xlsx');
        fprintf('最优角度已保存到文件: best_xi.xlsx\n');
    catch ME
        fprintf('保存Excel文件时出错: %s\n', ME.message);
    end

    % 将结果保存到base workspace供后续使用
    assignin('base', 'best_xi', best_xi);
    assignin('base', 'rotation_center_Tx', Tx);
    assignin('base', 'rotation_center_Ty', Ty);

    % 显示完成信息
    msgbox(sprintf('旋转角度计算完成！\n最优角度: %.6f 度\n已保存到 best_xi.xlsx', best_xi), ...
        '计算完成', 'help');

    % 第8步完成后自动进入第9步
    fprintf('第8步完成，自动进入第9步...\n');
    executeStep9();
end

%% 内嵌函数：更新旋转图像信息显示
function updateRotationImageInfo(~, ~, target_axes, img, info_text)
    % 获取当前鼠标位置
    current_point = get(target_axes, 'CurrentPoint');
    x_pos = current_point(1, 1);
    y_pos = current_point(1, 2);

    % 获取图像尺寸
    [img_height, img_width] = size(img);

    % 获取坐标轴范围
    xlim_range = get(target_axes, 'XLim');
    ylim_range = get(target_axes, 'YLim');

    % 检查鼠标是否在图像范围内
    if x_pos >= xlim_range(1) && x_pos <= xlim_range(2) && ...
       y_pos >= ylim_range(1) && y_pos <= ylim_range(2)

        % 将坐标转换为像素坐标
        pixel_x = round(x_pos);
        pixel_y = round(y_pos);

        % 确保坐标在有效范围内
        if pixel_x >= 1 && pixel_x <= img_width && ...
           pixel_y >= 1 && pixel_y <= img_height

            % 获取像素值
            intensity = img(pixel_y, pixel_x);

            % 更新信息显示
            info_str = sprintf('像素坐标: (%d, %d)\n相对强度: %.3f', ...
                pixel_x, pixel_y, intensity);
            set(info_text, 'String', info_str);
        else
            set(info_text, 'String', '像素坐标: (-, -)\n相对强度: -');
        end
    else
        set(info_text, 'String', '像素坐标: (-, -)\n相对强度: -');
    end
end

%% 内嵌函数：为旋转图像添加工具栏
function addRotationImageToolbar(parent_panel, target_axes)
    % 创建工具栏面板
    toolbar_panel = uipanel('Parent', parent_panel, ...
        'Position', [0.02, 0.92, 0.96, 0.06], ...
        'BorderType', 'none', ...
        'BackgroundColor', get(parent_panel, 'BackgroundColor'));

    % 缩放工具
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [5, 2, 30, 20], 'String', '🔍+', ...
        'FontSize', 8, 'ToolTip', '放大', ...
        'Callback', @(~,~) zoom(target_axes, 'on'));

    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [40, 2, 30, 20], 'String', '🔍-', ...
        'FontSize', 8, 'ToolTip', '缩小', ...
        'Callback', @(~,~) zoom(target_axes, 'out'));

    % 平移工具
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [75, 2, 30, 20], 'String', '✋', ...
        'FontSize', 8, 'ToolTip', '平移', ...
        'Callback', @(~,~) pan(target_axes, 'on'));

    % 重置视图
    uicontrol('Parent', toolbar_panel, 'Style', 'pushbutton', ...
        'Position', [110, 2, 40, 20], 'String', '重置', ...
        'FontSize', 8, 'ToolTip', '重置视图', ...
        'Callback', @(~,~) resetRotationView(target_axes));
end

%% 内嵌函数：重置旋转图像视图
function resetRotationView(target_axes)
    zoom(target_axes, 'out');
    zoom(target_axes, 'reset');
    pan(target_axes, 'off');
    zoom(target_axes, 'off');
    axis(target_axes, 'image');
end

%% 内嵌函数：第13步核心功能函数
function executeStep13()
    fprintf('开始第13步：波长-Y坐标关系图绘制...\n');

    try
        % 读取第11步的波长重构结果
        if evalin('base', 'exist(''lambda_matrix'', ''var'')')
            lambda_matrix = evalin('base', 'lambda_matrix');
            fprintf('成功读取波长重构数据，尺寸: %dx%dx%d\n', size(lambda_matrix));
        else
            error('无法获取波长重构数据lambda_matrix');
        end

        % 读取最优m值
        if evalin('base', 'exist(''m_best'', ''var'')')
            m_best = evalin('base', 'm_best');
            fprintf('成功读取最优m值: %d\n', m_best);
        else
            error('无法获取最优m值');
        end

        % 创建新图形窗口
        figure('Name', '波长-Y坐标关系图', 'NumberTitle', 'off', 'Position', [300, 150, 900, 700]);
        hold on;

        % 获取条纹数量
        num_stripes = size(lambda_matrix, 3);

        % 定义颜色映射
        colors = lines(min(num_stripes, 10)); % 最多显示10种不同颜色

        % 存储数据范围用于设置坐标轴
        all_wavelengths = [];
        all_y_coords = [];
        stripe_wavelengths = zeros(num_stripes, 1); % 存储每条纹的代表波长用于标注

        % 遍历每个条纹绘制波长-Y坐标关系
        for stripe_idx = 1:num_stripes
            % 提取该条纹的数据
            wavelengths = lambda_matrix(:, 1, stripe_idx);  % 波长值
            y_coords = lambda_matrix(:, 3, stripe_idx);     % Y坐标

            % 找到有效数据点
            valid_idx = wavelengths > 0;

            if sum(valid_idx) > 0
                % 提取有效数据
                valid_wavelengths = wavelengths(valid_idx);
                valid_y_coords = y_coords(valid_idx);

                % 收集数据范围
                all_wavelengths = [all_wavelengths; valid_wavelengths];
                all_y_coords = [all_y_coords; valid_y_coords];

                % 存储该条纹的中间波长位置用于标注
                stripe_wavelengths(stripe_idx) = mean(valid_wavelengths);

                % 选择颜色
                color_idx = mod(stripe_idx-1, size(colors, 1)) + 1;

                % 绘制该条纹的数据
                plot(valid_wavelengths, valid_y_coords, '-', ...
                    'LineWidth', 1.5, 'Color', colors(color_idx, :));
            end
        end

        % 设置图形属性
        xlabel('波长 (nm)', 'FontSize', 12, 'FontName', 'SimHei');
        ylabel('Y像素坐标', 'FontSize', 12, 'FontName', 'SimHei');
        title('不同干涉级次的波长-Y坐标关系', 'FontSize', 14, 'FontName', 'SimHei');
        grid on;
        grid minor;

        % 设置坐标轴范围 - 比数据稍微大一点，避免过多空白
        if ~isempty(all_wavelengths) && ~isempty(all_y_coords)
            % 计算数据范围
            wave_min = min(all_wavelengths);
            wave_max = max(all_wavelengths);
            y_min = min(all_y_coords);
            y_max = max(all_y_coords);

            % 添加边距 - 紧凑布局
            wave_margin = (wave_max - wave_min) * 0.05;
            y_margin_bottom = (y_max - y_min) * 0.05;
            y_margin_top = (y_max - y_min) * 0.08; % 上方只留8%空间，紧凑布局

            % 设置坐标轴范围
            xlim([wave_min - wave_margin, wave_max + wave_margin]);
            ylim([y_min - y_margin_bottom, y_max + y_margin_top]);

            % 添加条纹标注和开关控件
            addStripeAnnotations(stripe_wavelengths, y_max, m_best, num_stripes);
            addAnnotationToggle();

            fprintf('波长范围: %.6f - %.6f nm\n', wave_min, wave_max);
            fprintf('Y坐标范围: %.0f - %.0f 像素\n', y_min, y_max);
        end

        hold off;

        fprintf('✅ 波长-Y坐标关系图绘制完成！\n');
        fprintf('显示了 %d 个条纹的波长分布\n', num_stripes);
        fprintf('干涉级次范围: %d - %d\n', m_best - num_stripes, m_best - 1);

    catch ME
        fprintf('❌ 第13步执行失败：%s\n', ME.message);
    end

    fprintf('\n第13步完成！波长-Y坐标关系图已生成\n');

    % 第13步完成后自动进入第14步
    fprintf('第13步完成，自动进入第14步...\n');
    executeStep14();
end

%% 内嵌函数：添加条纹标注
function addStripeAnnotations(stripe_wavelengths, y_max, m_best, num_stripes)
    % 计算标注位置 - 紧凑布局
    y_range = ylim;
    annotation_y_top = y_range(2) - (y_range(2) - y_max) * 0.3;     % 顶部说明行
    annotation_y_stripe = y_max + (y_range(2) - y_max) * 0.1;       % 条纹编号行 - 紧贴条纹最上方

    % 添加顶部说明文字
    x_range = xlim;
    x_center = (x_range(1) + x_range(2)) / 2;  % 中心位置

    % 第一行：完整的说明文字 - 添加Tag用于开关控制
    top_text = sprintf('M = m - 干涉条纹编号     m = %d     干涉条纹编号如下：', m_best);
    h_top = text(x_center, annotation_y_top, top_text, ...
        'FontSize', 10, 'FontName', 'SimHei', 'FontWeight', 'bold', ...
        'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
        'Tag', 'annotation_text');

    % 第二行：为每个条纹添加编号标注
    for stripe_idx = 1:num_stripes
        if stripe_wavelengths(stripe_idx) > 0
            % 获取该条纹的波长位置
            x_pos = stripe_wavelengths(stripe_idx);

            % 添加条纹编号标注 - 紧贴条纹最上方，添加Tag用于开关控制
            h_stripe = text(x_pos, annotation_y_stripe, sprintf('%d', stripe_idx), ...
                'FontSize', 9, 'FontName', 'SimHei', ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
                'Color', 'blue', 'FontWeight', 'bold', ...
                'Tag', 'annotation_text');
        end
    end
end

%% 内嵌函数：添加标注开关控件
function addAnnotationToggle()
    % 获取当前坐标轴范围
    x_range = xlim;
    y_range = ylim;

    % 计算开关位置 - 右下角
    toggle_x = x_range(2) - (x_range(2) - x_range(1)) * 0.02;  % 右侧2%位置
    toggle_y = y_range(1) + (y_range(2) - y_range(1)) * 0.02;  % 底部2%位置

    % 创建小的开关按钮
    h_toggle = uicontrol('Style', 'checkbox', ...
        'String', '标注', ...
        'Value', 1, ...  % 默认显示标注
        'FontSize', 8, ...
        'FontName', 'SimHei', ...
        'Units', 'normalized', ...
        'Position', [0.92, 0.02, 0.06, 0.03], ...  % 右下角小尺寸
        'Callback', @toggleAnnotations);
end

%% 内嵌函数：切换标注显示/隐藏
function toggleAnnotations(src, ~)
    % 获取所有标注文字对象
    annotation_texts = findobj(gcf, 'Tag', 'annotation_text');

    % 根据复选框状态显示或隐藏标注
    if get(src, 'Value')
        set(annotation_texts, 'Visible', 'on');
    else
        set(annotation_texts, 'Visible', 'off');
    end
end

%% 内嵌函数：第14步核心功能函数
function executeStep14()
    fprintf('开始第14步：二次多项式拟合结果图绘制...\n');

    try
        % 读取第10步保存的数据
        if evalin('base', 'exist(''m_best'', ''var'')')
            m_best = evalin('base', 'm_best');
            fprintf('成功读取最优m值: %d\n', m_best);
        else
            error('无法获取最优m值');
        end

        if evalin('base', 'exist(''error_array'', ''var'')')
            error_array = evalin('base', 'error_array');
            fprintf('成功读取误差数组，长度: %d\n', length(error_array));
        else
            error('无法获取误差数组');
        end

        if evalin('base', 'exist(''data_fit_for_plot'', ''var'')')
            data_fit = evalin('base', 'data_fit_for_plot');
            fprintf('成功读取拟合数据，尺寸: %dx%d\n', size(data_fit));
        else
            error('无法获取拟合数据');
        end

        if evalin('base', 'exist(''m_range'', ''var'')')
            m_range = evalin('base', 'm_range');
        else
            m_range = 3300:1:3600;
        end

    catch ME
        fprintf('❌ 读取数据失败：%s\n', ME.message);
        return;
    end

    % 计算三个m值
    m_min = 3300;
    m_max = 3600;
    m_values = [round((m_best + m_min)/2), m_best, round((m_best + m_max)/2)];

    fprintf('绘制三个m值的拟合结果: %d, %d, %d\n', m_values(1), m_values(2), m_values(3));

    try
        % 创建新图形窗口
        figure('Name', '二次多项式拟合结果图', 'NumberTitle', 'off', 'Position', [150, 100, 800, 1000]);

        % 前三行：三个不同m值的拟合结果
        for i = 1:3
            current_m = m_values(i);

            % 计算当前m值下的修正波长 Mλ = λ×(m-Δm)
            y_current = zeros(size(data_fit, 1), 1);
            for j = 1:size(data_fit, 1)
                y_current(j) = data_fit(j, 4) * (current_m - data_fit(j, 5));
            end

            % 二次多项式拟合
            x_data = data_fit(:, 3);  % Y坐标
            P_current = polyfit(x_data, y_current, 2);

            % 主图 - 拟合结果
            subplot(4, 1, i);

            % 绘制散点
            plot(x_data, y_current, 'ko', 'MarkerSize', 4, 'MarkerFaceColor', 'k');
            hold on;

            % 绘制拟合曲线
            x_fit = linspace(min(x_data), max(x_data), 100);
            y_fit = polyval(P_current, x_fit);
            plot(x_fit, y_fit, 'r-', 'LineWidth', 2);

            % 设置标题和标签
            title(sprintf('m = %d', current_m), 'FontSize', 12, 'FontWeight', 'bold');
            ylabel('m*λ (nm)', 'FontSize', 10);
            grid on;

            % 计算残差
            y_pred = polyval(P_current, x_data);
            residuals = y_current - y_pred;
            sigma_value = std(residuals);

            % 在主图右下角添加1σ标注
            xlim_range = xlim;
            ylim_range = ylim;
            text(xlim_range(2) - (xlim_range(2) - xlim_range(1)) * 0.25, ...
                 ylim_range(1) + (ylim_range(2) - ylim_range(1)) * 0.15, ...
                 sprintf('1σ = %.5f', sigma_value), ...
                 'FontSize', 9, 'BackgroundColor', 'white', 'EdgeColor', 'black');

            hold off;

            fprintf('m=%d: 拟合完成，1σ = %.5f\n', current_m, sigma_value);
        end

        % 第四行：总残差曲线
        subplot(4, 1, 4);

        % 绘制误差曲线
        plot(m_range, error_array, 'k-', 'LineWidth', 2);
        hold on;

        % 标记最优点
        [min_error, min_idx] = min(error_array);
        plot(m_best, min_error, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');

        % 添加垂直线
        line([m_best, m_best], [0, max(error_array)], 'Color', 'r', 'LineStyle', '--', 'LineWidth', 1.5);

        % 添加标注
        text(m_best + 10, max(error_array) * 0.8, sprintf('m = %d', m_best), ...
             'FontSize', 10, 'FontWeight', 'bold', 'Color', 'red');

        % 设置标签
        xlabel('VIPA Order(number)', 'FontSize', 10);
        ylabel('Total residual(nm)', 'FontSize', 10);
        title('不同m值下的拟合残差', 'FontSize', 12, 'FontWeight', 'bold');
        grid on;

        hold off;

        % 调整子图间距
        sgtitle('二次多项式拟合结果图', 'FontSize', 14, 'FontWeight', 'bold');

        fprintf('✅ 第14步完成！二次多项式拟合结果图已生成\n');
        fprintf('显示了三个m值的拟合对比和总残差曲线\n');
        fprintf('最优m值: %d，对应最小残差: %.6f\n', m_best, min_error);

    catch ME
        fprintf('❌ 第14步执行失败：%s\n', ME.message);
    end
end

%% 内嵌函数：计算给定角度的旋转误差
function error_val = calculateRotationError(angle_deg, p_13, Tx, Ty)
    % 将角度转换为弧度
    xi = angle_deg / 180 * pi;

    % 构建旋转变换矩阵
    T = [cos(xi) sin(xi) Tx*cos(xi)+Ty*sin(xi)-Tx
        -sin(xi) cos(xi) -Tx*sin(xi)+Ty*cos(xi)-Ty
        0 0 1];

    % 计算每对点旋转后的横坐标差
    temp_p13 = p_13;  % 创建临时副本避免修改原数据
    for i = 1:size(temp_p13, 1)
        p1 = [temp_p13(i,1); temp_p13(i,2); 1];  % 第一个点[x1,y1,1]
        p2 = [temp_p13(i,3); temp_p13(i,4); 1];  % 对应的第二个点[x2,y2,1]
        a = T*p1;  % 旋转后的第一个点
        b = T*p2;  % 旋转后的第二个点
        temp_p13(i,5) = a(1) - b(1);  % 横坐标差值
    end

    % 计算总误差
    error_val = sum(abs(temp_p13(:,5)));
end

%% 自定义数据提示函数 - 波长显示6位小数
function output_txt = customDataTip(~, event_obj)
    % 获取数据点位置
    pos = get(event_obj, 'Position');
    wavelength = pos(1);  % X坐标是波长
    intensity = pos(2);   % Y坐标是强度

    % 创建自定义提示文本，波长显示6位小数
    output_txt = {['X: ', sprintf('%.6f', wavelength)], ...
                  ['Y: ', sprintf('%.6f', intensity)]};
end